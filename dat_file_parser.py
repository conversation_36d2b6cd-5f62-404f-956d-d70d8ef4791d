# -*- coding: utf-8 -*-
"""Parser for .dat format diode measurement files."""

import re
import numpy as np
import logging
from typing import Dict, Tu<PERSON>, List


def parse_dat_file(file_path: str) -> Dict:
    """
    Parse .dat format diode measurement file.
    
    Args:
        file_path (str): Path to the .dat file
        
    Returns:
        Dict: Parsed data in the same format as JSON data for compatibility
    """
    logging.info(f"Parsing .dat file: {file_path}")
    
    with open(file_path, 'r') as f:
        lines = f.readlines()
    
    # Parse instance parameters
    instance_params = {}
    data_start_index = -1
    
    for i, line in enumerate(lines):
        line = line.strip()
        
        # Parse Instance line: Instance{T=25.0,die_x=4.0,die_y=-4.0,l=10.0,mr=16.0,w=10.0}
        if line.startswith('Instance{') and line.endswith('}'):
            instance_content = line[9:-1]  # Remove 'Instance{' and '}'
            params = instance_content.split(',')
            for param in params:
                if '=' in param:
                    key, value = param.split('=', 1)
                    try:
                        instance_params[key.strip()] = float(value.strip())
                    except ValueError:
                        instance_params[key.strip()] = value.strip()
        
        # Find data start marker: [vpn,Ip(ref_vn=generator(0.00000)=1.00000)]
        if line.startswith('[vpn,Ip(') and line.endswith(')]'):
            data_start_index = i + 1
            break
    
    if data_start_index == -1:
        raise ValueError("Could not find data start marker '[vpn,Ip(...)]' in file")
    
    # Parse IV data
    voltages = []
    currents = []
    
    for i in range(data_start_index, len(lines)):
        line = lines[i].strip()
        if not line or line.startswith('//'):
            continue
            
        try:
            parts = line.split(',')
            if len(parts) >= 2:
                voltage = float(parts[0])
                current = float(parts[1])
                voltages.append(voltage)
                currents.append(current)
        except ValueError:
            logging.warning(f"Skipping invalid data line: {line}")
            continue
    
    if not voltages or not currents:
        raise ValueError("No valid IV data found in file")
    
    # Convert to numpy arrays
    v_ca_a = np.array(voltages)
    i_c_a = np.array(currents)
    
    # Generate temperature key from instance parameters
    temperature = instance_params.get('T', 25.0)  # Default to 25°C if not found
    temp_key = f"T{temperature}K"
    
    logging.info(f"Parsed {len(voltages)} data points for temperature {temperature}K")
    logging.info(f"Voltage range: {min(voltages):.3f}V to {max(voltages):.3f}V")
    logging.info(f"Current range: {min(currents):.3e}A to {max(currents):.3e}A")
    
    # Create dummy capacitance data (zeros) since .dat file doesn't contain capacitance
    c_ca_a = np.zeros_like(v_ca_a)
    
    # Format data in the same structure as JSON format for compatibility
    parsed_data = {
        temp_key: {
            'V_CA': v_ca_a.tolist(),  # Use uppercase keys to match expected format
            'I_C': i_c_a.tolist(),
            'C_CA': c_ca_a.tolist(),
            'v_ca_a': v_ca_a.tolist(),  # Keep lowercase for backward compatibility
            'i_c_a': i_c_a.tolist(),
            'c_ca_a': c_ca_a.tolist(),
            'instance_params': instance_params,
            'temperature_K': temperature + 273.15,  # Convert to Kelvin
            'device_geometry': {
                'length': instance_params.get('l', None),
                'width': instance_params.get('w', None),
                'multiplier': instance_params.get('mr', None)
            }
        }
    }
    
    return parsed_data


def detect_file_format(file_path: str) -> str:
    """
    Detect whether file is JSON or DAT format.
    
    Args:
        file_path (str): Path to the file
        
    Returns:
        str: 'json' or 'dat'
    """
    try:
        with open(file_path, 'r') as f:
            first_lines = [f.readline().strip() for _ in range(5)]
        
        # Check for JSON format
        first_content = ''.join(first_lines)
        if first_content.startswith('{') or first_content.startswith('['):
            return 'json'
        
        # Check for DAT format markers
        for line in first_lines:
            if line.startswith('Instance{') or line.startswith('ProbeInfo{') or line.startswith('Version{'):
                return 'dat'
        
        # Default to JSON if uncertain
        return 'json'
        
    except Exception:
        return 'json'


def load_measurement_data(file_path: str) -> Dict:
    """
    Load measurement data from either JSON or DAT format.
    
    Args:
        file_path (str): Path to the measurement file
        
    Returns:
        Dict: Parsed measurement data
    """
    file_format = detect_file_format(file_path)
    logging.info(f"Detected file format: {file_format}")
    
    if file_format == 'dat':
        return parse_dat_file(file_path)
    else:
        # Use existing JSON parser
        import json
        with open(file_path, 'r') as f:
            return json.load(f)


if __name__ == "__main__":
    # Test the parser
    import sys
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
        try:
            data = load_measurement_data(file_path)
            print(f"Successfully parsed {file_path}")
            print(f"Found {len(data)} temperature measurements:")
            for key in data.keys():
                temp_data = data[key]
                print(f"  {key}: {len(temp_data['v_ca_a'])} data points")
                if 'instance_params' in temp_data:
                    print(f"    Device parameters: {temp_data['instance_params']}")
        except Exception as e:
            print(f"Error parsing {file_path}: {e}")
    else:
        print("Usage: python dat_file_parser.py <file_path>")
