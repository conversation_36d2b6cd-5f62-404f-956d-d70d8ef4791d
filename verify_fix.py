#!/usr/bin/env python3
"""
验证GUI修复的简单脚本
"""

import sys
import os

def verify_gui_fix():
    """验证GUI修复"""
    print("🔍 验证GUI修复...")
    
    try:
        # 1. 检查能否导入GUI类
        from diode_pyqt_gui import DiodeParameterExtractorGUI
        print("✅ GUI类导入成功")
        
        # 2. 检查关键修复点
        import inspect
        
        # 检查_line_edit_finished方法是否使用正确的属性引用
        source = inspect.getsource(DiodeParameterExtractorGUI._line_edit_finished)
        if "self.param_widgets[key]['line_edit']" in source:
            print("✅ _line_edit_finished方法修复正确")
        else:
            print("❌ _line_edit_finished方法仍有问题")
            return False
            
        # 检查_enable_controls方法
        source = inspect.getsource(DiodeParameterExtractorGUI._enable_controls)
        if "self.param_widgets[key]['slider']" in source:
            print("✅ _enable_controls方法修复正确")
        else:
            print("❌ _enable_controls方法仍有问题")
            return False
            
        # 检查update_plot方法
        source = inspect.getsource(DiodeParameterExtractorGUI.update_plot)
        if "self.param_widgets[key]['line_edit']" in source:
            print("✅ update_plot方法修复正确")
        else:
            print("❌ update_plot方法仍有问题")
            return False
            
        print("✅ 所有关键修复点验证通过")
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 验证过程出错: {e}")
        return False

def check_dependencies():
    """检查依赖"""
    print("🔍 检查依赖...")
    
    required_modules = ['PyQt5', 'numpy', 'scipy', 'matplotlib']
    missing = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module} 可用")
        except ImportError:
            print(f"❌ {module} 缺失")
            missing.append(module)
    
    if missing:
        print(f"\n⚠️  缺失依赖: {', '.join(missing)}")
        print("请运行: pip install " + " ".join(missing))
        return False
    
    return True

def main():
    """主函数"""
    print("🚀 GUI修复验证")
    print("=" * 40)
    
    # 检查依赖
    deps_ok = check_dependencies()
    
    # 验证修复
    fix_ok = verify_gui_fix() if deps_ok else False
    
    print("=" * 40)
    
    if deps_ok and fix_ok:
        print("🎉 GUI修复验证成功！")
        print("\n📋 使用方法:")
        print("  python diode_pyqt_gui.py    # 启动GUI")
        print("  python extract_diode_model_parameters.py  # 命令行版本")
        return 0
    else:
        print("❌ 验证失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
