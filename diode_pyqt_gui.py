import sys
import os
import json
import threading
import numpy as np
import traceback # Import traceback module
import configparser
from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLineEdit, QLabel, QTextEdit, QFileDialog, QGroupBox, QMenu, QSlider, QSplitter, QStackedWidget, QProgressBar, QGridLayout, QComboBox, QMessageBox, QDialog, QFormLayout, QDialogButtonBox
import logging
from PyQt5.QtCore import Qt, pyqtSignal as Signal, QObject, QTimer, QThread

# Matplotlib integration
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas, NavigationToolbar2QT as NavigationToolbar
from matplotlib.figure import Figure
import matplotlib.pyplot as plt
from matplotlib.widgets import SpanSelector

# Import the core processing function and modelling classes
from extract_diode_model_parameters import process_diode_measurement
from diode_modelling import DiodeModelIsotherm, ideal_diode_model
from diode_utils import crop_data_range_to_x
from diode_equations import NOMINAL_TEMPERATURE_K
from diode_plots import plot_measurements_overview, plot_vca_ic, plot_vca_cca,plot_vca_cca_for_presentation, plot_vca_ic_ideal, plot_vca_ic_r, plot_T_is

def _load_config(config_file='config.ini'):
    config = configparser.ConfigParser()
    if not os.path.exists(config_file):
        return None
    try:
        config.read(config_file)
    except configparser.Error:
        return None
    return config

_config = _load_config()

# Dark theme stylesheet
dark_stylesheet = """
QWidget {
    background-color: #2E2E2E;
    color: #FFFFFF;
    border: 1px solid #5A5A5A;
}
QMainWindow {
    background-color: #2E2E2E;
}
QGroupBox {
    background-color: #3C3C3C;
    border: 1px solid #5A5A5A;
    margin-top: 20px;
}
QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top center;
    padding: 5px 10px;
    background-color: #5A5A5A;
    color: #FFFFFF;
    border-radius: 5px;
}
QPushButton {
    background-color: #5A5A5A;
    color: #FFFFFF;
    border: 1px solid #777777;
    padding: 5px;
    border-radius: 5px;
}
QPushButton:hover {
    background-color: #777777;
}
QPushButton:pressed {
    background-color: #888888;
}
QLineEdit, QTextEdit, QDoubleSpinBox, QComboBox {
    background-color: #3C3C3C;
    color: #FFFFFF;
    border: 1px solid #5A5A5A;
    padding: 5px;
    border-radius: 3px;
}
QComboBox::drop-down {
    border: 0px;
}
QComboBox::down-arrow {
    image: url(noimg);
}
QLabel {
    border: 0px;
}
QTextEdit {
    background-color: #2E2E2E;
}
QMessageBox {
    background-color: #3C3C3C;
}
QSlider::groove:horizontal {
    border: 1px solid #bbb;
    background: white;
    height: 10px;
    border-radius: 4px;
}
QSlider::handle:horizontal {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #eee, stop:1 #ccc);
    border: 1px solid #777;
    width: 18px;
    margin: -2px 0;
    border-radius: 4px;
}
"""

# Light theme stylesheet
light_stylesheet = """
QWidget {
    background-color: #F0F0F0;
    color: #333333;
    border: 1px solid #CCCCCC;
}
QMainWindow {
    background-color: #F0F0F0;
}
QGroupBox {
    background-color: #FFFFFF;
    border: 1px solid #CCCCCC;
    margin-top: 20px;
}
QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top center;
    padding: 5px 10px;
    background-color: #DDDDDD;
    color: #333333;
    border-radius: 5px;
}
QPushButton {
    background-color: #E0E0E0;
    color: #333333;
    border: 1px solid #BBBBBB;
    padding: 5px;
    border-radius: 5px;
}
QPushButton:hover {
    background-color: #D0D0D0;
}
QPushButton:pressed {
    background-color: #C0C0C0;
}
QLineEdit, QTextEdit, QDoubleSpinBox, QComboBox {
    background-color: #FFFFFF;
    color: #333333;
    border: 1px solid #CCCCCC;
    padding: 5px;
    border-radius: 3px;
}
QComboBox::drop-down {
    border: 0px;
}
QComboBox::down-arrow {
    image: url(noimg);
}
QLabel {
    border: 0px;
}
QTextEdit {
    background-color: #F8F8F8;
}
QMessageBox {
    background-color: #FFFFFF;
}
QSlider::groove:horizontal {
    border: 1px solid #bbb;
    background: #E0E0E0;
    height: 10px;
    border-radius: 4px;
}
QSlider::handle:horizontal {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #eee, stop:1 #ccc);
    border: 1px solid #777;
    width: 18px;
    margin: -2px 0;
    border-radius: 4px;
}
"""

class Stream(QObject):
    newText = Signal(str)

    def write(self, text):
        self.newText.emit(str(text))

    def flush(self):
        pass

class Worker(QObject):
    completed = Signal(list, dict, int)
    failed = Signal(str)
    progress = Signal(str) # For logging messages
    progress_value = Signal(int) # For progress bar

    def __init__(self, data_file, results_file, plot_dir):
        super().__init__()
        self.data_file = data_file
        self.results_file = results_file
        self.plot_dir = plot_dir

    def run(self):
        try:
            self.progress.emit("Starting diode parameter extraction... This may take a moment.\n")
            os.makedirs(self.plot_dir, exist_ok=True)
            plot_data_list, extracted_params = process_diode_measurement(measurements_fname=self.data_file, results_fname=self.results_file, progress_callback=self.progress_value.emit)
            
            # Find the manual tuning plot data index
            closest_temp_diff = float('inf')
            manual_tuning_plot_data_index = -1
            for idx, plot_data in enumerate(plot_data_list):
                if plot_data['plot_type'] == 'vca_ic':
                    T = plot_data['model'].T
                    if abs(T - NOMINAL_TEMPERATURE_K) < closest_temp_diff:
                        closest_temp_diff = abs(T - NOMINAL_TEMPERATURE_K) # Corrected to use NOMINAL_TEMPERATURE_K
                        manual_tuning_plot_data_index = idx

            self.completed.emit(plot_data_list, extracted_params, manual_tuning_plot_data_index)
        except Exception as e:
            error_message = f"Error during extraction: {e}\n{''.join(traceback.format_exc())}"
            self.failed.emit(error_message)


class DiodeParameterExtractorGUI(QMainWindow):
    update_log_signal = Signal(str)
    extraction_completed_signal = Signal(list, dict, int)
    extraction_failed_signal = Signal(str)
    enable_controls_signal = Signal()

    def __init__(self):
        super().__init__()
        self.setWindowTitle("Diode Model Parameter Extractor (PyQt5)")
        self.setGeometry(100, 100, 1600, 1000)

        self.main_splitter = QSplitter(Qt.Horizontal) # Main horizontal splitter
        self.setCentralWidget(self.main_splitter)

        self.statusBar = self.statusBar() # Initialize status bar
        self.statusBar.showMessage("Ready")

        self.param_keys = ["I_S", "m", "R_S", "CJO", "VJ", "M_GRADING", "FC"]
        self.param_ranges = {
            "I_S": (1e-15, 1e-6, 'log'),
            "m": (0.5, 5.0, 'linear'),
            "R_S": (0.01, 100.0, 'log'),
            "CJO": (1e-12, 1e-9, 'log'),
            "VJ": (0.1, 1.5, 'linear'),
            "M_GRADING": (0.1, 0.5, 'linear'),
            "FC": (0.1, 0.9, 'linear')
        }
        self.SLIDER_RANGE = (0, 1000) # Min and max values for the QSlider

        self.layouts = {
            "1x1": (1, 1),
            "1x2": (1, 2),
            "2x1": (2, 1),
            "2x2": (2, 2),
            "2x3": (2, 3),
            "3x2": (3, 2),
            "3x3": (3, 3),
            "4x4": (4, 4)
        }

        self._create_widgets()

        # Configure logging to output to the QTextEdit
        log_stream = Stream(newText=self.update_log_signal.emit)
        logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s', stream=log_stream)
        sys.stdout = log_stream # Redirect stdout to the log stream
        sys.stderr = log_stream # Redirect stderr to the log stream
        self.update_log_signal.connect(self.append_text)

    def _create_widgets(self):
        # Create widgets first
        self.config_group = QGroupBox("Input/Output Configuration")
        self.config_layout = QVBoxLayout(self.config_group)

        self.control_layout = QHBoxLayout()
        self.control_widget = QWidget()
        self.control_widget.setLayout(self.control_layout)

        self.params_group = QGroupBox("Model Parameters (Manual Tuning)")
        self.params_layout = QGridLayout(self.params_group)

        self.log_group = QGroupBox("Output Log")
        self.log_layout = QVBoxLayout(self.log_group)
        self.output_text = QTextEdit()
        self.output_text.setReadOnly(True)
        self.log_layout.addWidget(self.output_text)

        # Input/Output Configuration
        self.data_file_path = QLineEdit(os.path.join(os.getcwd(), _config.get('Paths', 'data_file', fallback='data.json')) if _config else os.path.join(os.getcwd(), "data.json"))
        self.results_file_path = QLineEdit(os.path.join(os.getcwd(), _config.get('Paths', 'results_file', fallback='model.json')) if _config else os.path.join(os.getcwd(), "model.json"))
        self.plot_dir_path = QLineEdit(os.path.join(os.getcwd(), _config.get('Paths', 'plot_directory', fallback='plots')) if _config else os.path.join(os.getcwd(), "plots"))
        self._add_file_selector(self.config_layout, "Measurement Data File:", self.data_file_path, self.browse_data_file)
        self._add_file_selector(self.config_layout, "Results Output File:", self.results_file_path, None)
        self._add_file_selector(self.config_layout, "Plots Output Directory:", self.plot_dir_path, self.browse_plot_dir)

        # Control Buttons
        self.run_button = QPushButton("Run Extraction")
        self.run_button.clicked.connect(self.run_extraction)
        self.control_layout.addWidget(self.run_button)

        self.view_plots_button = QPushButton("View Plots Folder")
        self.view_plots_button.clicked.connect(self.view_plots)
        self.control_layout.addWidget(self.view_plots_button)

        self.theme_toggle_button = QPushButton("Toggle Theme")
        self.theme_toggle_button.clicked.connect(self._toggle_theme)
        self.control_layout.addWidget(self.theme_toggle_button)

        self.progress_bar = QProgressBar()
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setFormat("Extraction Progress: %p%")
        self.progress_bar.setValue(0)
        self.control_layout.addWidget(self.progress_bar)

        # Model Parameters (Manual Tuning)
        self.param_widgets = {} # To store references to {key: {'label': label, 'line_edit': line_edit, 'slider': slider}}

        # Add existing parameters with default values
        default_values = {
            "I_S": 1e-12,
            "m": 1.1,
            "R_S": 1.0,
            "CJO": 1e-12,
            "VJ": 0.7,
            "M_GRADING": 0.5,
            "FC": 0.5
        }
        for key in self.param_keys:
            min_val, max_val, scale = self.param_ranges[key]
            initial_value = default_values.get(key, (min_val + max_val) / 2)
            self._add_parameter_widget(key, min_val, max_val, scale, initial_value)

        self.r_squared_label = QLabel("R² (I-V Fit): N/A")
        self.params_layout.addWidget(self.r_squared_label, len(self.param_keys), 0, 1, 2)

        self.reset_params_button = QPushButton("Reset Parameters")
        self.reset_params_button.clicked.connect(self._reset_parameters)
        self.params_layout.addWidget(self.reset_params_button, len(self.param_keys), 2)

        # Add buttons for dynamic parameter management
        self.add_param_button = QPushButton("Add Parameter")
        self.add_param_button.clicked.connect(self._add_parameter_dialog)
        self.control_layout.addWidget(self.add_param_button)

        self.remove_param_button = QPushButton("Remove Parameter")
        self.remove_param_button.clicked.connect(self._remove_parameter_dialog)
        self.control_layout.addWidget(self.remove_param_button)

        self.left_panel_splitter = QSplitter(Qt.Vertical)
        self.left_panel_splitter.addWidget(self.config_group)
        self.left_panel_splitter.addWidget(self.control_widget)
        self.left_panel_splitter.addWidget(self.params_group)
        self.left_panel_splitter.addWidget(self.log_group)
        
        # Add the left panel splitter to the main splitter
        self.main_splitter.addWidget(self.left_panel_splitter)

        # --- Right Panel ---
        self.right_panel_widget = QWidget()
        self.right_panel_layout = QVBoxLayout(self.right_panel_widget)
        self.main_splitter.addWidget(self.right_panel_widget)

        # Plotting Area (Right Panel)
        self.plot_stack = QStackedWidget()
        self.right_panel_layout.addWidget(self.plot_stack)

        # Plot Navigation and Layout Controls
        plot_nav_layout = QHBoxLayout()
        self.prev_page_button = QPushButton("Previous Plot")
        self.prev_page_button.clicked.connect(self._prev_page)
        plot_nav_layout.addWidget(self.prev_page_button)

        self.page_label = QLabel("Page 0/0")
        plot_nav_layout.addWidget(self.page_label)

        self.next_page_button = QPushButton("Next Plot")
        self.next_page_button.clicked.connect(self._next_page)
        plot_nav_layout.addWidget(self.next_page_button)

        plot_nav_layout.addStretch(1) # Add stretch to push layout combo to the right

        layout_label = QLabel("Plot Layout:")
        plot_nav_layout.addWidget(layout_label)
        self.layout_combo = QComboBox()
        self.layout_combo.addItems(self.layouts.keys())
        self.layout_combo.setCurrentText("2x2") # Set default to 2x2
        self.layout_combo.currentTextChanged.connect(self._render_plots)
        plot_nav_layout.addWidget(self.layout_combo)

        self.right_panel_layout.addLayout(plot_nav_layout)

        # Set initial sizes for the splitters
        self.left_panel_splitter.setSizes([150, 100, 200, 300]) # Example sizes, adjust as needed
        self.main_splitter.setSizes([400, 1200]) # Example sizes, adjust as needed

        self.all_plot_data = []
        self.current_page_index = 0
        self.pages = []
        self.manual_tuning_plot_data_index = -1
        self._plot_data_changed = False
        self._updating_gui = False

        self.extraction_completed_signal.connect(self._on_extraction_completed)
        self.extraction_failed_signal.connect(self._on_extraction_failed)
        self.enable_controls_signal.connect(self._enable_controls)
        self._update_page_navigation_buttons()
        sys.excepthook = self._handle_exception

        self.is_dark_theme = True # Default to dark theme
        if _config and 'GUI' in _config:
            if _config.get('GUI', 'theme', fallback='dark') == "light":
                self.is_dark_theme = False
        self._apply_theme()

    def _slider_pos_to_value(self, pos, key):
        min_val, max_val, scale = self.param_ranges[key]
        slider_min, slider_max = self.SLIDER_RANGE
        if scale == 'log':
            log_min, log_max = np.log10(min_val), np.log10(max_val)
            log_val = log_min + (log_max - log_min) * (pos / (slider_max - slider_min))
            return 10**log_val
        else:
            return min_val + (max_val - min_val) * (pos / (slider_max - slider_min))

    def _value_to_slider_pos(self, value, key):
        min_val, max_val, scale = self.param_ranges[key]
        slider_min, slider_max = self.SLIDER_RANGE
        value = max(min_val, min(value, max_val))
        if scale == 'log':
            log_min, log_max = np.log10(min_val), np.log10(max_val)
            log_val = np.log10(value)
            pos = (log_val - log_min) / (log_max - log_min) * (slider_max - slider_min)
            return int(pos)
        else:
            pos = (value - min_val) / (max_val - min_val) * (slider_max - slider_min)
            return int(pos)

    def _slider_value_changed(self, key, value):
        if self._updating_gui: return
        self.param_widgets[key]['line_edit'].editingFinished.disconnect()
        new_val = self._slider_pos_to_value(value, key)
        self.param_widgets[key]['line_edit'].setText(f"{new_val:.4e}")
        self.param_widgets[key]['line_edit'].editingFinished.connect(lambda k=key: self._line_edit_finished(k))
        if hasattr(self, '_update_timer'):
            self._update_timer.stop()
        self._update_timer = QTimer()
        self._update_timer.setSingleShot(True)
        self._update_timer.timeout.connect(self.update_plot)
        self._update_timer.start(150)

    def _line_edit_finished(self, key):
        if self._updating_gui: return
        self.param_widgets[key]['slider'].valueChanged.disconnect()
        try:
            text_val = float(self.param_widgets[key]['line_edit'].text())
            min_val, max_val, _ = self.param_ranges[key]
            clamped_val = max(min_val, min(text_val, max_val))
            if clamped_val != text_val:
                self.param_widgets[key]['line_edit'].setText(f"{clamped_val:.4e}")
            new_pos = self._value_to_slider_pos(clamped_val, key)
            self.param_widgets[key]['slider'].setValue(new_pos)
        except ValueError:
            current_pos = self.param_widgets[key]['slider'].value()
            current_val = self._slider_pos_to_value(current_pos, key)
            self.param_widgets[key]['line_edit'].setText(f"{current_val:.4e}")
        self.param_widgets[key]['slider'].valueChanged.connect(lambda value, k=key: self._slider_value_changed(k, value))
        self.update_plot()

    def _toggle_theme(self):
        self.is_dark_theme = not self.is_dark_theme
        self._apply_theme()

    def _apply_theme(self):
        app = QApplication.instance()
        if self.is_dark_theme:
            plt.style.use('dark_background')
            plt.rcParams['grid.color'] = '#A9A9A9'
            plt.rcParams['grid.linestyle'] = '--'
            plt.rcParams['grid.linewidth'] = 0.5
            app.setStyleSheet(dark_stylesheet)
        else:
            plt.style.use('default')
            plt.rcParams['axes.facecolor'] = 'white' # Explicitly set plot background to white
            plt.rcParams['figure.facecolor'] = 'white' # Explicitly set figure background to white
            plt.rcParams['text.color'] = 'black' # Set text color to black
            plt.rcParams['axes.labelcolor'] = 'black' # Set axis label color to black
            plt.rcParams['xtick.color'] = 'black' # Set x-tick color to black
            plt.rcParams['ytick.color'] = 'black' # Set y-tick color to black
            plt.rcParams['grid.color'] = '#CCCCCC' # A lighter gray for light theme
            plt.rcParams['grid.linestyle'] = '-' # Solid line for light theme
            plt.rcParams['grid.linewidth'] = 0.8 # Slightly thicker for visibility
            app.setStyleSheet(light_stylesheet)
        self._render_plots()

        # Save theme preference
        if _config:
            try:
                _config.set('GUI', 'theme', "dark" if self.is_dark_theme else "light")
                with open('config.ini', 'w') as configfile:
                    _config.write(configfile)
            except Exception as e:
                logging.warning(f"Could not save theme preference: {e}")


    def _calculate_r_squared(self, y_true, y_pred):
        if len(y_true) == 0 or len(y_true) != len(y_pred): return np.nan
        ss_res = np.sum((y_true - y_pred) ** 2)
        ss_tot = np.sum((y_true - np.mean(y_true)) ** 2)
        if ss_tot == 0: return 1.0 if ss_res == 0 else 0.0
        return 1 - (ss_res / ss_tot)

    def _update_r_squared_label(self):
        if self.manual_tuning_plot_data_index != -1 and self.all_plot_data:
            plot_data = self.all_plot_data[self.manual_tuning_plot_data_index]
            model = plot_data['model']
            v_ca_a, i_c_a_measured = plot_data['v_ca_a'], plot_data['i_c_a']
            i_c_a_model = model.calc_ic_diode_ohmic_a(v_ca_a)
            v_ca_cropped, i_c_a_measured_cropped = crop_data_range_to_x(v_ca_a, i_c_a_measured, model.vca_lim_lower_ic, model.vca_lim_upper_ic)
            _, i_c_a_model_cropped = crop_data_range_to_x(v_ca_a, i_c_a_model, model.vca_lim_lower_ic, model.vca_lim_upper_ic)
            r_squared = self._calculate_r_squared(i_c_a_measured_cropped, i_c_a_model_cropped)
            self.r_squared_label.setText(f"R² (I-V Fit): {r_squared:.4f}" if not np.isnan(r_squared) else "R² (I-V Fit): N/A")

    def _handle_exception(self, exc_type, exc_value, exc_traceback):
        if issubclass(exc_type, KeyboardInterrupt):
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        error_message = f"Unhandled exception:\n{exc_type.__name__}: {exc_value}\n{''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))}"
        self.update_log_signal.emit(error_message)
        logging.critical("An unhandled error occurred. The application may be unstable. Please check the Output Log for details and consider restarting.")
        self.statusBar.showMessage("Unhandled exception occurred. Check log.")
        self.enable_controls_signal.emit()

    def _add_file_selector(self, layout, label_text, line_edit_widget, browse_func):
        h_layout = QHBoxLayout()
        h_layout.addWidget(QLabel(label_text))
        h_layout.addWidget(line_edit_widget)
        if browse_func:
            browse_button = QPushButton("Browse")
            browse_button.clicked.connect(browse_func)
            h_layout.addWidget(browse_button)
        layout.addLayout(h_layout)

    

    def _add_parameter_widget(self, key, min_val, max_val, scale, initial_value=None):
        row = self.params_layout.rowCount()

        label = QLabel(key)
        line_edit = QLineEdit()
        if initial_value is not None:
            line_edit.setText(f"{initial_value:.4e}")
        else:
            # Set a default value to prevent empty text
            default_val = (min_val + max_val) / 2 if scale == 'linear' else np.sqrt(min_val * max_val)
            line_edit.setText(f"{default_val:.4e}")
            initial_value = default_val

        slider = QSlider(Qt.Horizontal)
        slider.setRange(self.SLIDER_RANGE[0], self.SLIDER_RANGE[1])

        self.params_layout.addWidget(label, row, 0)
        self.params_layout.addWidget(line_edit, row, 1)
        self.params_layout.addWidget(slider, row, 2)

        self.param_widgets[key] = {
            'label': label,
            'line_edit': line_edit,
            'slider': slider,
            'min_val': min_val,
            'max_val': max_val,
            'scale': scale
        }

        # Add to param_keys only if not already present
        if key not in self.param_keys:
            self.param_keys.append(key)
        self.param_ranges[key] = (min_val, max_val, scale)

        # Set initial slider value before connecting signals
        if initial_value is not None:
            slider.setValue(self._value_to_slider_pos(initial_value, key))

        # Connect signals after setting initial values
        line_edit.editingFinished.connect(lambda k=key: self._line_edit_finished(k))
        slider.valueChanged.connect(lambda value, k=key: self._slider_value_changed(k, value))

    def _remove_parameter_widget(self, key):
        if key in self.param_widgets:
            widgets = self.param_widgets[key]
            self.params_layout.removeWidget(widgets['label'])
            self.params_layout.removeWidget(widgets['line_edit'])
            self.params_layout.removeWidget(widgets['slider'])
            widgets['label'].deleteLater()
            widgets['line_edit'].deleteLater()
            widgets['slider'].deleteLater()
            del self.param_widgets[key]
            self.param_keys.remove(key)
            del self.param_ranges[key]
            self._rearrange_param_layout()

    def _rearrange_param_layout(self):
        # Clear existing layout
        for i in reversed(range(self.params_layout.count())):
            widget = self.params_layout.itemAt(i).widget()
            if widget:
                widget.setParent(None)

        # Re-add widgets based on updated param_keys order
        for row, key in enumerate(self.param_keys):
            widgets = self.param_widgets[key]
            self.params_layout.addWidget(widgets['label'], row, 0)
            self.params_layout.addWidget(widgets['line_edit'], row, 1)
            self.params_layout.addWidget(widgets['slider'], row, 2)
        
        # Re-add R^2 label and Reset button at the end
        self.params_layout.addWidget(self.r_squared_label, len(self.param_keys), 0, 1, 2)
        self.params_layout.addWidget(self.reset_params_button, len(self.param_keys), 2)

    def _add_parameter_dialog(self):
        dialog = QDialog(self)
        dialog.setWindowTitle("Add New Parameter")
        dialog_layout = QVBoxLayout(dialog)

        form_layout = QFormLayout()
        name_input = QLineEdit()
        initial_value_input = QLineEdit()
        min_value_input = QLineEdit()
        max_value_input = QLineEdit()
        scale_combo = QComboBox()
        scale_combo.addItems(["linear", "log"])

        form_layout.addRow("Parameter Name:", name_input)
        form_layout.addRow("Initial Value:", initial_value_input)
        form_layout.addRow("Min Value:", min_value_input)
        form_layout.addRow("Max Value:", max_value_input)
        form_layout.addRow("Scale Type:", scale_combo)
        dialog_layout.addLayout(form_layout)

        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel, Qt.Horizontal, dialog)
        buttons.accepted.connect(dialog.accept)
        buttons.rejected.connect(dialog.reject)
        dialog_layout.addWidget(buttons)

        if dialog.exec_() == QDialog.Accepted:
            name = name_input.text()
            try:
                initial_value = float(initial_value_input.text())
                min_val = float(min_value_input.text())
                max_val = float(max_value_input.text())
                scale = scale_combo.currentText()

                if name and name not in self.param_keys:
                    self._add_parameter_widget(name, min_val, max_val, scale, initial_value)
                    logging.info(f"Added new parameter: {name}")
                else:
                    logging.warning("Parameter name is empty or already exists.")
            except ValueError:
                logging.error("Invalid input for parameter values. Please enter numbers.")

    def _remove_parameter_dialog(self):
        dialog = QDialog(self)
        dialog.setWindowTitle("Remove Parameter")
        dialog_layout = QVBoxLayout(dialog)

        param_combo = QComboBox()
        param_combo.addItems(self.param_keys)
        dialog_layout.addWidget(QLabel("Select Parameter to Remove:"))
        dialog_layout.addWidget(param_combo)

        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel, Qt.Horizontal, dialog)
        buttons.accepted.connect(dialog.accept)
        buttons.rejected.connect(dialog.reject)
        dialog_layout.addWidget(buttons)

        if dialog.exec_() == QDialog.Accepted:
            selected_param = param_combo.currentText()
            if selected_param:
                self._remove_parameter_widget(selected_param)
                logging.info(f"Removed parameter: {selected_param}")
            else:
                logging.warning("No parameter selected for removal.")

    def append_text(self, text):
        self.output_text.moveCursor(self.output_text.textCursor().End)
        self.output_text.insertPlainText(text)
        # Update status bar with the last line of the log
        last_line = text.strip().split('\n')[-1]
        if last_line:
            self.statusBar.showMessage(last_line)

    def browse_data_file(self):
        file_path, _ = QFileDialog.getOpenFileName(self, "Select Measurement Data File", os.getcwd(), "JSON files (*.json);;All files (*.*)")
        if file_path: self.data_file_path.setText(file_path)

    def browse_plot_dir(self):
        dir_path = QFileDialog.getExistingDirectory(self, "Select Plots Directory", os.getcwd())
        if dir_path: self.plot_dir_path.setText(dir_path)

    def run_extraction(self):
        data_file = self.data_file_path.text()
        if not data_file:
            logging.warning("Input Error: Please select a measurement data file.")
            self.statusBar.showMessage("Input Error: Please select a measurement data file.")
            return

        self.output_text.clear()
        self.update_log_signal.emit("Starting diode parameter extraction... This may take a moment.\n")
        self.progress_bar.setValue(0) # Reset progress bar
        self.run_button.setEnabled(False)
        for key in self.param_keys:
            if key in self.param_widgets:
                self.param_widgets[key]['slider'].setEnabled(False)
                self.param_widgets[key]['line_edit'].setEnabled(False)
        self.all_plot_data = []
        self._plot_data_changed = True
        self.current_page_index = 0
        for i in reversed(range(self.plot_stack.count())):
            widget_to_remove = self.plot_stack.widget(i)
            self.plot_stack.removeWidget(widget_to_remove)
            widget_to_remove.deleteLater()
        self.pages = []
        self._update_page_navigation_buttons()

        self.thread = QThread()
        self.worker = Worker(data_file, self.results_file_path.text(), self.plot_dir_path.text())
        self.worker.moveToThread(self.thread)

        self.thread.started.connect(self.worker.run)
        self.worker.completed.connect(self._on_extraction_completed)
        self.worker.failed.connect(self._on_extraction_failed)
        self.worker.progress_value.connect(self.progress_bar.setValue)
        self.worker.completed.connect(self.thread.quit)
        self.worker.failed.connect(self.thread.quit)
        self.thread.finished.connect(self.thread.deleteLater)
        self.worker.completed.connect(lambda: self.enable_controls_signal.emit())
        self.worker.failed.connect(lambda: self.enable_controls_signal.emit())

        self.thread.start()

    

    def _on_extraction_completed(self, plot_data_list, extracted_params, manual_tuning_plot_data_index):
        self._updating_gui = True # Prevent update_plot from being called during programmatic updates
        self.all_plot_data = plot_data_list
        self.initial_extracted_params = extracted_params # Store initial extracted parameters
        self.manual_tuning_plot_data_index = manual_tuning_plot_data_index
        self._plot_data_changed = True
        # Initialize all parameters to 0.0 to prevent empty strings
        for key in list(self.param_widgets.keys()): # Iterate over a copy of keys as dict might change size
            self.param_widgets[key]['line_edit'].setText("0.0")
            self.param_widgets[key]['slider'].setValue(self._value_to_slider_pos(0.0, key)) # Set slider to 0.0 position

        for key, value in extracted_params.items():
            if key in self.param_widgets:
                self.param_widgets[key]['line_edit'].setText(f"{value:.4e}")
                self.param_widgets[key]['slider'].setValue(self._value_to_slider_pos(value, key))
            else:
                # Add new parameter if it exists in extracted_params but not in GUI
                # Use default min/max/scale for newly added parameters if not explicitly defined
                min_val = 1e-15 # Default min for new parameters
                max_val = 1e-6  # Default max for new parameters
                scale = 'log'   # Default scale for new parameters
                self._add_parameter_widget(key, min_val, max_val, scale, initial_value=value)
        self._update_r_squared_label()
        self._render_plots()
        self.update_log_signal.emit(f"\nExtraction complete! Check '{self.results_file_path.text()}' for results and '{self.plot_dir_path.text()}' for plots.\n")
        logging.info("Diode parameter extraction completed successfully!")
        self.progress_bar.setValue(100) # Set progress to 100% on completion
        self.statusBar.showMessage("Extraction completed successfully!")
        self._updating_gui = False # Re-enable update_plot for user interactions

    def _on_extraction_failed(self, error_message):
        self.update_log_signal.emit(f"\nERROR: {error_message}\n")
        logging.critical("An error occurred during diode parameter extraction. Please check the Output Log for details.")
        self.progress_bar.setValue(0) # Reset progress to 0% on failure

    def _enable_controls(self):
        self.run_button.setEnabled(True)
        for key in self.param_keys:
            if key in self.param_widgets:
                self.param_widgets[key]['slider'].setEnabled(True)
                self.param_widgets[key]['line_edit'].setEnabled(True)

    def _render_plots(self):
        self.update_log_signal.emit("Rendering plots...\n")
        try:
            # Clear existing plots only if data has changed or layout has changed
            if self._plot_data_changed or \
               not hasattr(self, '_last_layout') or self.layout_combo.currentText() != self._last_layout:
                for i in reversed(range(self.plot_stack.count())):
                    widget_to_remove = self.plot_stack.widget(i)
                    self.plot_stack.removeWidget(widget_to_remove)
                    widget_to_remove.deleteLater()
                self.pages = []
                self._plot_data_changed = False
                self._last_layout = self.layout_combo.currentText()

            if not self.all_plot_data:
                self.page_label.setText("Page 0/0")
                self._update_page_navigation_buttons()
                return

            rows, cols = self.layouts[self.layout_combo.currentText()]
            plots_per_page = rows * cols
            current_plot_data_index = 0

            # If pages are not yet created, create them
            if not self.pages:
                while current_plot_data_index < len(self.all_plot_data):
                    page_widget = QWidget()
                    page_layout = QGridLayout(page_widget)
                    page_figure = Figure(figsize=(cols * 4, rows * 3))
                    if self.is_dark_theme:
                        page_figure.set_facecolor('#2E2E2E')
                    else:
                        page_figure.set_facecolor('white')
                    page_canvas = FigureCanvas(page_figure)
                    page_layout.addWidget(page_canvas, 0, 0, rows, cols)
                    toolbar = NavigationToolbar(page_canvas, page_widget)
                    page_layout.addWidget(toolbar, rows, 0, 1, cols)
                    axes_on_page = []
                    for r in range(rows):
                        for c in range(cols):
                            if current_plot_data_index < len(self.all_plot_data):
                                ax = page_figure.add_subplot(rows, cols, len(axes_on_page) + 1)
                                if self.is_dark_theme:
                                    ax.set_facecolor('#2E2E2E')
                                else:
                                    ax.set_facecolor('white')
                                axes_on_page.append(ax)
                                ax.grid(True)
                                page_canvas.mpl_connect('button_press_event', lambda event, current_ax=ax: self._on_plot_right_click(event, current_ax))
                                current_plot_data_index += 1
                            else: break
                    page_figure.tight_layout()
                    self.pages.append(page_widget)
                    self.plot_stack.addWidget(page_widget)
            
            # Update existing plots
            current_plot_data_index = 0
            for page_idx, page_widget in enumerate(self.pages):
                if self.is_dark_theme:
                    page_widget.setStyleSheet("background-color: #2E2E2E;")
                else:
                    page_widget.setStyleSheet("background-color: white;")
                page_figure = page_widget.findChild(FigureCanvas).figure
                if self.is_dark_theme:
                    page_figure.set_facecolor('#2E2E2E')
                else:
                    page_figure.set_facecolor('white')
                page_canvas = page_widget.findChild(FigureCanvas)
                if self.is_dark_theme:
                    page_canvas.setStyleSheet("background-color: #2E2E2E;")
                else:
                    page_canvas.setStyleSheet("background-color: white;")
                for ax in page_figure.get_axes():
                    ax.clear() # Clear existing plot content
                    if self.is_dark_theme:
                        ax.set_facecolor('#2E2E2E')
                    else:
                        ax.set_facecolor('white')
                    if current_plot_data_index < len(self.all_plot_data):
                        plot_data = self.all_plot_data[current_plot_data_index]
                        ax.plot_data = plot_data # Store plot_data on ax for right-click context
                        plot_type = plot_data['plot_type']
                        if plot_type == 'overview': plot_measurements_overview(plot_data['meas_dict'], ax, ax.twinx())
                        elif plot_type == 'vca_ic':
                            plot_vca_ic(plot_data['v_ca_a'], plot_data['i_c_a'], plot_data['model'], ax, ax.twinx())
                            if current_plot_data_index == self.manual_tuning_plot_data_index:
                                # Re-initialize SpanSelector if it's the manual tuning plot
                                if hasattr(self, 'span') and self.span.ax == ax:
                                    self.span.clear() # Clear previous span
                                self.span = SpanSelector(ax, self._on_span_select, 'horizontal', useblit=True)
                                self.span.extents = (plot_data['model'].vca_lim_lower_ic, plot_data['model'].vca_lim_upper_ic)
                                self.span.active = True
                        elif plot_type == 'vca_cca': plot_vca_cca(plot_data['v_ca_a'], plot_data['i_c_a'], plot_data['c_ca_a'], plot_data['model'], ax, ax.twinx())
                        elif plot_type == 'vca_cca_for_presentation': plot_vca_cca_for_presentation(plot_data['v_ca_a'], plot_data['i_c_a'], plot_data['c_ca_a'], plot_data['model'], ax, ax.twinx())
                        elif plot_type == 'vca_ic_ideal': plot_vca_ic_ideal(plot_data['v_ca_a'], plot_data['i_c_a'], plot_data['model'], ax)
                        elif plot_type == 'vca_ic_r': plot_vca_ic_r(plot_data['v_ca_a'], plot_data['i_c_a'], plot_data['model'], ax, ax.twinx())
                        elif plot_type == 'T_is': plot_T_is(plot_data['T_a'], plot_data['i_s_a'], plot_data['model'], ax)
                        current_plot_data_index += 1
                    else:
                        ax.set_visible(False) # Hide unused subplots
                page_figure.canvas.draw_idle() # Redraw the canvas
            
            self.plot_stack.setCurrentIndex(self.current_page_index)
            self._update_page_navigation_buttons()
            self.update_log_signal.emit("Plots rendered successfully.\n")
        except Exception as e:
            error_message = f"Error during plot rendering:\n{e}\n{''.join(traceback.format_exc())}"
            self.update_log_signal.emit(error_message)
            logging.critical("An error occurred during plot rendering. Please check the Output Log for details.")
            self.statusBar.showMessage("Plotting error. Check log.")

    def _update_page_navigation_buttons(self):
        total_pages = len(self.pages)
        self.page_label.setText(f"Page {self.current_page_index + 1}/{total_pages}")
        self.prev_page_button.setEnabled(self.current_page_index > 0)
        self.next_page_button.setEnabled(self.current_page_index < total_pages - 1)

    def _prev_page(self):
        if self.current_page_index > 0:
            self.current_page_index -= 1
            self.plot_stack.setCurrentIndex(self.current_page_index)
            self._update_page_navigation_buttons()

    def _next_page(self):
        if self.current_page_index < len(self.pages) - 1:
            self.current_page_index += 1
            self.plot_stack.setCurrentIndex(self.current_page_index)
            self._update_page_navigation_buttons()

    def update_plot(self):
        self.update_log_signal.emit("Attempting to update plot...\n")
        try:
            if self.manual_tuning_plot_data_index == -1 or not self.all_plot_data: return
            params = {key: float(self.param_widgets[key]['line_edit'].text()) for key in self.param_keys if key in self.param_widgets}
            self.update_log_signal.emit(f"Updating model with: {', '.join([f'{k}={v:.2e}' for k, v in params.items()])}\n")
            manual_tuning_plot_data = self.all_plot_data[self.manual_tuning_plot_data_index]
            manual_tuning_model = manual_tuning_plot_data['model']
            manual_tuning_model.set_parameters(**params)
            self._update_r_squared_label()
            self._render_plots()
            self.update_log_signal.emit("Plot updated successfully.\n")
        except Exception as e:
            error_message = f"""Error during manual plot update:
{e}
{''.join(traceback.format_exc())}"""
            self.update_log_signal.emit(error_message)
            logging.critical("An error occurred during manual tuning. Please check the Output Log for details.")
            self.statusBar.showMessage("Manual tuning error. Check log.")

    def view_plots(self):
        plot_dir = self.plot_dir_path.text()
        if not os.path.exists(plot_dir):
            logging.warning(f"Plots directory '{plot_dir}' does not exist yet.")
            self.statusBar.showMessage(f"Warning: Plots directory '{plot_dir}' does not exist yet.")
            return
        try:
            if sys.platform == "win32": os.startfile(plot_dir)
            elif sys.platform == "darwin": os.system(f"open {plot_dir}")
            else: os.system(f"xdg-open {plot_dir}")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Could not open plots directory: {e}")

    def _set_x_scale(self, ax, scale_type):
        try:
            if scale_type == 'log' and (np.any(np.array(ax.get_xlim()) <= 0) or np.any(ax.get_lines()[0].get_xdata() <= 0)):
                logging.warning("Cannot set X-axis to log scale: data contains non-positive values.")
                self.statusBar.showMessage("Log Scale Error: X-axis data contains non-positive values.")
                return
            ax.set_xscale(scale_type, linthresh=1e-9 if scale_type == 'symlog' else None)
            ax.figure.canvas.draw_idle()
        except Exception as e:
            logging.error(f"Error setting X-axis scale: {e}")
            self.statusBar.showMessage(f"Error setting X-axis scale: {e}")

    def _set_y_scale(self, ax, scale_type):
        try:
            if scale_type == 'log' and (np.any(np.array(ax.get_ylim()) <= 0) or np.any(ax.get_lines()[0].get_ydata() <= 0)):
                logging.warning("Cannot set Y-axis to log scale: data contains non-positive values.")
                self.statusBar.showMessage("Log Scale Error: Y-axis data contains non-positive values.")
                return
            ax.set_yscale(scale_type, linthresh=1e-9 if scale_type == 'symlog' else None)
            ax.figure.canvas.draw_idle()
        except Exception as e:
            logging.error(f"Error setting Y-axis scale: {e}")
            self.statusBar.showMessage(f"Error setting Y-axis scale: {e}")

    def _on_plot_right_click(self, event, ax):
        if event.button == 3:
            menu = QMenu(self)
            actions = {
                "X-Axis: Linear": lambda: self._set_x_scale(ax, 'linear'),
                "X-Axis: Log": lambda: self._set_x_scale(ax, 'log'),
                "X-Axis: Symmetric Log": lambda: self._set_x_scale(ax, 'symlog'),
                "Y-Axis: Linear": lambda: self._set_y_scale(ax, 'linear'),
                "Y-Axis: Log": lambda: self._set_y_scale(ax, 'log'),
                "Y-Axis: Symmetric Log": lambda: self._set_y_scale(ax, 'symlog'),
                "Calculate Differential Resistance": lambda: self._calculate_differential_resistance_for_ax(ax),
                "Reset View": lambda: ax.autoscale_view()
            }
            for title, func in actions.items():
                if "Y-Axis" in title or "Reset" in title: menu.addSeparator()
                menu.addAction(title).triggered.connect(func)
            menu.exec_(self.mapToGlobal(event.guiEvent.globalPos()))
            ax.figure.canvas.draw_idle()

    def _on_span_select(self, xmin, xmax):
        self.update_log_signal.emit(f"Span selected: {xmin:.4f}V to {xmax:.4f}V\n")
        try:
            if self.manual_tuning_plot_data_index == -1 or not self.all_plot_data: return
            plot_data = self.all_plot_data[self.manual_tuning_plot_data_index]
            model, v_ca_a, i_c_a, T = plot_data['model'], plot_data['v_ca_a'], plot_data['i_c_a'], plot_data['model'].T
            new_i_s, new_m = ideal_diode_model(v_ca_a, i_c_a, xmin, xmax, T)
            
            for k in ["I_S", "m"]:
                if k in self.param_widgets:
                    self.param_widgets[k]['slider'].valueChanged.disconnect()
                    self.param_widgets[k]['line_edit'].editingFinished.disconnect()

            if "I_S" in self.param_widgets:
                self.param_widgets["I_S"]['line_edit'].setText(f"{new_i_s:.4e}")
                self.param_widgets["I_S"]['slider'].setValue(self._value_to_slider_pos(new_i_s, "I_S"))
            if "m" in self.param_widgets:
                self.param_widgets["m"]['line_edit'].setText(f"{new_m:.4e}")
                self.param_widgets["m"]['slider'].setValue(self._value_to_slider_pos(new_m, "m"))

            for k in ["I_S", "m"]:
                if k in self.param_widgets:
                    self.param_widgets[k]['slider'].valueChanged.connect(lambda value, key=k: self._slider_value_changed(key, value))
                    self.param_widgets[k]['line_edit'].editingFinished.connect(lambda key=k: self._line_edit_finished(key))

            model.params['I_S'] = new_i_s
            model.params['m'] = new_m
            model.vca_lim_lower_ic, model.vca_lim_upper_ic = xmin, xmax
            self._update_r_squared_label()
            self._render_plots()
            self.update_log_signal.emit("Model parameters updated from span selection.\n")
        except Exception as e:
            error_message = f"""Error during span selection update:
{e}
{''.join(traceback.format_exc())}"""
            self.update_log_signal.emit(error_message)
            logging.critical("An error occurred during span selection. Please check the Output Log for details.")
            self.statusBar.showMessage("Span selection error. Check log.")

    def _calculate_differential_resistance_for_ax(self, ax):
        self.update_log_signal.emit("Calculating differential resistance for selected plot...\n")
        try:
            plot_data = getattr(ax, 'plot_data', None)
            if not plot_data or plot_data['plot_type'] not in ['vca_ic', 'vca_ic_ideal', 'vca_ic_r']:
                self.update_log_signal.emit("Differential resistance calculation not supported for this plot type.\n")
                return
            v_ca_a, i_c_a = plot_data['v_ca_a'], plot_data['i_c_a']
            dI, dV = np.diff(i_c_a), np.diff(v_ca_a)
            valid_indices = np.abs(dI) > 1e-12
            if np.any(valid_indices):
                r_diff, v_mid = dV[valid_indices] / dI[valid_indices], (v_ca_a[:-1] + v_ca_a[1:])[valid_indices] / 2
                diff_res_fig = Figure(figsize=(8, 6))
                diff_res_canvas = FigureCanvas(diff_res_fig)
                diff_res_ax = diff_res_fig.add_subplot(111)
                diff_res_ax.plot(v_mid, r_diff, 'b-', label='Differential Resistance (dV/dI)')
                diff_res_ax.set_xlabel('Voltage (V)'), diff_res_ax.set_ylabel('Differential Resistance (Ohm)')
                diff_res_ax.set_title('Differential Resistance vs. Voltage'), diff_res_ax.grid(True), diff_res_ax.legend()
                diff_res_fig.tight_layout()
                diff_res_window = QMainWindow(self)
                diff_res_window.setWindowTitle("Differential Resistance"), diff_res_window.setCentralWidget(FigureCanvas(diff_res_fig))
                diff_res_window.show()
                self.update_log_signal.emit("Differential resistance calculation complete.\n")
            else:
                self.update_log_signal.emit("No valid data points for differential resistance calculation (dI is too small).\n")
        except Exception as e:
            error_message = f"""Error during differential resistance calculation:
{e}
{''.join(traceback.format_exc())}"""
            self.update_log_signal.emit(error_message)
            logging.critical("An error occurred during differential resistance calculation. Please check the Output Log for details.")
            logging.critical("An error occurred during differential resistance calculation. Please check the Output Log for details.")
            self.statusBar.showMessage("Differential resistance calculation error. Check log.")

    def _reset_parameters(self):
        if hasattr(self, 'initial_extracted_params') and self.initial_extracted_params:
            for key, value in self.initial_extracted_params.items():
                if key in self.param_widgets:
                    self.param_widgets[key]['line_edit'].setText(f"{value:.4e}")
                    self.param_widgets[key]['slider'].setValue(self._value_to_slider_pos(value, key))
                else:
                    # If the parameter was not initially present, add it
                    min_val = 1e-15 # Default min for new parameters
                    max_val = 1e-6  # Default max for new parameters
                    scale = 'log'   # Default scale for new parameters
                    self._add_parameter_widget(key, min_val, max_val, scale, initial_value=value)
            self.update_log_signal.emit("Parameters reset to initially extracted values.\n")
            self.update_plot()
        else:
            self.update_log_signal.emit("No initial parameters to reset to. Please run extraction first.\n")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    window = DiodeParameterExtractorGUI()
    window.show()
    sys.exit(app.exec_())