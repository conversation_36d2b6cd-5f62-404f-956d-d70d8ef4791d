import sys
import os
import json
import threading
import numpy as np
import traceback # Import traceback module
import configparser
from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLineEdit, QLabel, QTextEdit, QFileDialog, QGroupBox, QMenu, QSlider, QSplitter, QStackedWidget, QProgressBar, QGridLayout, QComboBox, QMessageBox, QDialog, QFormLayout, QDialogButtonBox, QShortcut, QScrollArea, QCheckBox
import logging
from PyQt5.QtCore import Qt, pyqtSignal as Signal, QObject, QTimer, QThread
from PyQt5.QtGui import QKeySequence

# Matplotlib integration
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas, NavigationToolbar2QT as NavigationToolbar
from matplotlib.figure import Figure
import matplotlib.pyplot as plt
from matplotlib.widgets import SpanSelector

# Import the core processing function and modelling classes
from extract_diode_model_parameters import process_diode_measurement
from diode_modelling import DiodeModelIsotherm, ideal_diode_model
from diode_utils import crop_data_range_to_x
from diode_equations import NOMINAL_TEMPERATURE_K
from diode_plots import plot_measurements_overview, plot_vca_ic, plot_vca_cca,plot_vca_cca_for_presentation, plot_vca_ic_ideal, plot_vca_ic_r, plot_T_is

def _load_config(config_file='config.ini'):
    config = configparser.ConfigParser()
    if not os.path.exists(config_file):
        return None
    try:
        config.read(config_file)
    except configparser.Error:
        return None
    return config

_config = _load_config()

# Dark theme stylesheet
dark_stylesheet = """
QWidget {
    background-color: #2E2E2E;
    color: #FFFFFF;
    border: 1px solid #5A5A5A;
}
QMainWindow {
    background-color: #2E2E2E;
}
QGroupBox {
    background-color: #3C3C3C;
    border: 1px solid #5A5A5A;
    margin-top: 20px;
}
QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top center;
    padding: 5px 10px;
    background-color: #5A5A5A;
    color: #FFFFFF;
    border-radius: 5px;
}
QPushButton {
    background-color: #5A5A5A;
    color: #FFFFFF;
    border: 1px solid #777777;
    padding: 5px;
    border-radius: 5px;
}
QPushButton:hover {
    background-color: #777777;
}
QPushButton:pressed {
    background-color: #888888;
}
QLineEdit, QTextEdit, QDoubleSpinBox, QComboBox {
    background-color: #1E1E1E;
    color: #FFFF00;
    border: 2px solid #777777;
    padding: 6px;
    border-radius: 3px;
    font-weight: bold;
    font-size: 12px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}
QLineEdit:focus, QComboBox:focus {
    border: 2px solid #4A90E2;
    background-color: #2A2A2A;
    color: #FFFFFF;
}
QComboBox::drop-down {
    border: 0px;
}
QComboBox::down-arrow {
    image: url(noimg);
}
QLabel {
    border: 0px;
}
QTextEdit {
    background-color: #2E2E2E;
}
QMessageBox {
    background-color: #3C3C3C;
}
QSlider::groove:horizontal {
    border: 1px solid #bbb;
    background: white;
    height: 10px;
    border-radius: 4px;
}
QSlider::handle:horizontal {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #eee, stop:1 #ccc);
    border: 1px solid #777;
    width: 18px;
    margin: -2px 0;
    border-radius: 4px;
}
"""

# Light theme stylesheet
light_stylesheet = """
QWidget {
    background-color: #F0F0F0;
    color: #333333;
    border: 1px solid #CCCCCC;
}
QMainWindow {
    background-color: #F0F0F0;
}
QGroupBox {
    background-color: #FFFFFF;
    border: 1px solid #CCCCCC;
    margin-top: 20px;
}
QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top center;
    padding: 5px 10px;
    background-color: #DDDDDD;
    color: #333333;
    border-radius: 5px;
}
QPushButton {
    background-color: #E0E0E0;
    color: #333333;
    border: 1px solid #BBBBBB;
    padding: 5px;
    border-radius: 5px;
}
QPushButton:hover {
    background-color: #D0D0D0;
}
QPushButton:pressed {
    background-color: #C0C0C0;
}
QLineEdit, QTextEdit, QDoubleSpinBox, QComboBox {
    background-color: #FFFFFF;
    color: #000080;
    border: 2px solid #AAAAAA;
    padding: 6px;
    border-radius: 3px;
    font-weight: bold;
    font-size: 12px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}
QLineEdit:focus, QComboBox:focus {
    border: 2px solid #4A90E2;
    background-color: #F0F8FF;
    color: #000000;
}
QComboBox::drop-down {
    border: 0px;
}
QComboBox::down-arrow {
    image: url(noimg);
}
QLabel {
    border: 0px;
}
QTextEdit {
    background-color: #F8F8F8;
}
QMessageBox {
    background-color: #FFFFFF;
}
QSlider::groove:horizontal {
    border: 1px solid #bbb;
    background: #E0E0E0;
    height: 10px;
    border-radius: 4px;
}
QSlider::handle:horizontal {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #eee, stop:1 #ccc);
    border: 1px solid #777;
    width: 18px;
    margin: -2px 0;
    border-radius: 4px;
}
"""

class Stream(QObject):
    newText = Signal(str)

    def write(self, text):
        self.newText.emit(str(text))

    def flush(self):
        pass

class Worker(QObject):
    completed = Signal(list, dict, int)
    failed = Signal(str)
    progress = Signal(str) # For logging messages
    progress_value = Signal(int) # For progress bar

    def __init__(self, data_file, results_file, plot_dir, selected_test_data=None):
        super().__init__()
        self.data_file = data_file
        self.results_file = results_file
        self.plot_dir = plot_dir
        self.selected_test_data = selected_test_data

    def run(self):
        try:
            self.progress.emit("Starting diode parameter extraction... This may take a moment.\n")
            os.makedirs(self.plot_dir, exist_ok=True)
            plot_data_list, extracted_params = process_diode_measurement(measurements_fname=self.data_file, results_fname=self.results_file, progress_callback=self.progress_value.emit, selected_test_data=self.selected_test_data)
            
            # Find the manual tuning plot data index
            closest_temp_diff = float('inf')
            manual_tuning_plot_data_index = -1
            for idx, plot_data in enumerate(plot_data_list):
                if plot_data['plot_type'] == 'vca_ic':
                    T = plot_data['model'].T
                    if abs(T - NOMINAL_TEMPERATURE_K) < closest_temp_diff:
                        closest_temp_diff = abs(T - NOMINAL_TEMPERATURE_K) # Corrected to use NOMINAL_TEMPERATURE_K
                        manual_tuning_plot_data_index = idx

            self.completed.emit(plot_data_list, extracted_params, manual_tuning_plot_data_index)
        except Exception as e:
            error_message = f"Error during extraction: {e}\n{''.join(traceback.format_exc())}"
            self.failed.emit(error_message)


class DiodeParameterExtractorGUI(QMainWindow):
    update_log_signal = Signal(str)
    extraction_completed_signal = Signal(list, dict, int)
    extraction_failed_signal = Signal(str)
    enable_controls_signal = Signal()

    def __init__(self):
        super().__init__()
        self.setWindowTitle("Diode Model Parameter Extractor (PyQt5)")
        # 增大窗口尺寸，并设置最小尺寸
        self.setGeometry(50, 50, 1920, 1080)
        self.setMinimumSize(1200, 800)

        self.main_splitter = QSplitter(Qt.Horizontal) # Main horizontal splitter
        self.setCentralWidget(self.main_splitter)

        self.statusBar = self.statusBar() # Initialize status bar
        self.statusBar.showMessage("Ready | 快捷键: F11=全屏 Ctrl+H=侧边栏 Ctrl+T=主题 Ctrl+R=运行 ←→=切换页面")

        # HSPICE Level=3 standard parameter names (default visible parameters)
        self.param_keys = ["IS", "N", "RS", "CJO", "VJ", "M", "FC"]
        self.param_ranges = {
            # HSPICE Level=3 DC parameters
            "IS": (1e-15, 1e-6, 'log'),
            "N": (0.5, 5.0, 'linear'),
            "RS": (0.01, 100.0, 'log'),
            # HSPICE Level=3 capacitance parameters
            "CJO": (1e-12, 1e-9, 'log'),
            "VJ": (0.1, 1.5, 'linear'),
            "M": (0.1, 0.5, 'linear'),
            "FC": (0.1, 0.9, 'linear'),
            # HSPICE Level=3 breakdown parameters
            "BV": (1.0, 1000.0, 'log'),
            "IBV": (1e-6, 1e-3, 'log'),
            # HSPICE Level=3 time parameter
            "TT": (1e-12, 1e-6, 'log'),
            # HSPICE Level=3 temperature parameters
            "EG": (0.5, 2.0, 'linear'),
            "XTI": (1.0, 5.0, 'linear')
        }
        self.SLIDER_RANGE = (0, 1000) # Min and max values for the QSlider

        self.layouts = {
            "1x1": (1, 1),
            "1x2": (1, 2),
            "2x1": (2, 1),
            "2x2": (2, 2),
            "2x3": (2, 3),
            "3x2": (3, 2),
            "3x3": (3, 3),
            "4x4": (4, 4)
        }

        self._create_widgets()

        # Configure logging to output to the QTextEdit
        log_stream = Stream(newText=self.update_log_signal.emit)
        logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s', stream=log_stream)
        sys.stdout = log_stream # Redirect stdout to the log stream
        sys.stderr = log_stream # Redirect stderr to the log stream
        self.update_log_signal.connect(self.append_text)

    def _create_widgets(self):
        # Create widgets first
        self.config_group = QGroupBox("Input/Output Configuration")
        self.config_layout = QVBoxLayout(self.config_group)

        # 使用网格布局来实现两行按钮排列
        self.control_layout = QGridLayout()
        self.control_widget = QWidget()
        self.control_widget.setLayout(self.control_layout)
        # 设置紧凑的间距
        self.control_layout.setSpacing(5)
        self.control_layout.setContentsMargins(5, 5, 5, 5)

        self.params_group = QGroupBox("Model Parameters (Manual Tuning)")
        # 增加参数组的高度，给更多参数留出空间
        self.params_group.setMinimumHeight(500)
        self.params_group.setMaximumHeight(600)

        # 创建滚动区域来容纳参数
        self.params_scroll = QScrollArea()
        self.params_scroll.setWidgetResizable(True)
        self.params_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.params_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        # 创建参数容器widget
        self.params_widget = QWidget()
        self.params_layout = QGridLayout(self.params_widget)
        # 设置更宽松的间距，让参数更容易看清
        self.params_layout.setSpacing(5)
        self.params_layout.setContentsMargins(10, 10, 10, 10)
        # 设置列的拉伸因子，让滑块列不会过度拉伸
        self.params_layout.setColumnStretch(0, 0)  # 标签列不拉伸
        self.params_layout.setColumnStretch(1, 0)  # 输入框列不拉伸
        self.params_layout.setColumnStretch(2, 1)  # 滑块列适度拉伸

        # 将参数widget放入滚动区域
        self.params_scroll.setWidget(self.params_widget)

        # 将滚动区域放入参数组
        params_group_layout = QVBoxLayout(self.params_group)
        params_group_layout.addWidget(self.params_scroll)

        self.log_group = QGroupBox("Output Log")
        self.log_layout = QVBoxLayout(self.log_group)
        self.output_text = QTextEdit()
        self.output_text.setReadOnly(True)
        self.log_layout.addWidget(self.output_text)

        # Input/Output Configuration
        self.data_file_path = QLineEdit(os.path.join(os.getcwd(), _config.get('Paths', 'data_file', fallback='data/data.dat')) if _config else os.path.join(os.getcwd(), "data/data.dat"))
        self.results_file_path = QLineEdit(os.path.join(os.getcwd(), _config.get('Paths', 'results_file', fallback='model.json')) if _config else os.path.join(os.getcwd(), "model.json"))
        self.plot_dir_path = QLineEdit(os.path.join(os.getcwd(), _config.get('Paths', 'plot_directory', fallback='plots')) if _config else os.path.join(os.getcwd(), "plots"))
        self._add_file_selector(self.config_layout, "Measurement Data File:", self.data_file_path, self.browse_data_file)
        self._add_file_selector(self.config_layout, "Results Output File:", self.results_file_path, None)
        self._add_file_selector(self.config_layout, "Plots Output Directory:", self.plot_dir_path, self.browse_plot_dir)

        # 添加测试数据选择器
        self._add_test_data_selector()

        # Control Buttons - 两行排列
        # 第一行按钮
        self.run_button = QPushButton("Run Extraction")
        self.run_button.clicked.connect(self.run_extraction)
        self.control_layout.addWidget(self.run_button, 0, 0)

        self.view_plots_button = QPushButton("View Plots Folder")
        self.view_plots_button.clicked.connect(self.view_plots)
        self.control_layout.addWidget(self.view_plots_button, 0, 1)

        self.theme_toggle_button = QPushButton("Toggle Theme")
        self.theme_toggle_button.clicked.connect(self._toggle_theme)
        self.control_layout.addWidget(self.theme_toggle_button, 0, 2)

        # 第二行按钮
        self.toggle_sidebar_button = QPushButton("Hide Sidebar")
        self.toggle_sidebar_button.clicked.connect(self._toggle_sidebar)
        self.control_layout.addWidget(self.toggle_sidebar_button, 1, 0)
        self.sidebar_visible = True

        self.fullscreen_plot_button = QPushButton("Fullscreen Plot")
        self.fullscreen_plot_button.clicked.connect(self._toggle_fullscreen_plot)
        self.control_layout.addWidget(self.fullscreen_plot_button, 1, 1)
        self.plot_fullscreen = False

        # 进度条跨越两列
        self.progress_bar = QProgressBar()
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setFormat("Extraction Progress: %p%")
        self.progress_bar.setValue(0)
        self.control_layout.addWidget(self.progress_bar, 1, 2)

        # Model Parameters (Manual Tuning)
        self.param_widgets = {} # To store references to {key: {'label': label, 'line_edit': line_edit, 'slider': slider}}

        # Add existing parameters with HSPICE Level=3 default values
        default_values = {
            "IS": 1e-14,
            "N": 1.0,
            "RS": 1.0,
            "CJO": 1e-12,
            "VJ": 0.7,
            "M": 0.5,
            "FC": 0.5,
            "BV": 100.0,
            "IBV": 1e-3,
            "TT": 1e-9,
            "EG": 1.17,
            "XTI": 3.0
        }
        for key in self.param_keys:
            min_val, max_val, scale = self.param_ranges[key]
            initial_value = default_values.get(key, (min_val + max_val) / 2)
            self._add_parameter_widget(key, min_val, max_val, scale, initial_value)

        self.r_squared_label = QLabel("R² (I-V Fit): N/A")
        self.params_layout.addWidget(self.r_squared_label, len(self.param_keys), 0, 1, 2)

        self.reset_params_button = QPushButton("Reset Parameters")
        self.reset_params_button.clicked.connect(self._reset_parameters)
        self.params_layout.addWidget(self.reset_params_button, len(self.param_keys), 2)

        # Add buttons for dynamic parameter management
        self.add_param_button = QPushButton("Add Parameter from Model")
        self.add_param_button.clicked.connect(self._add_parameter_dialog)
        self.add_param_button.setToolTip("Add a parameter from the extracted model to the tuning panel")
        self.control_layout.addWidget(self.add_param_button)

        self.remove_param_button = QPushButton("Remove Parameter")
        self.remove_param_button.clicked.connect(self._remove_parameter_dialog)
        self.control_layout.addWidget(self.remove_param_button)

        self.left_panel_splitter = QSplitter(Qt.Vertical)
        self.left_panel_splitter.addWidget(self.config_group)
        self.left_panel_splitter.addWidget(self.control_widget)
        self.left_panel_splitter.addWidget(self.params_group)
        self.left_panel_splitter.addWidget(self.log_group)
        
        # Add the left panel splitter to the main splitter
        self.main_splitter.addWidget(self.left_panel_splitter)

        # --- Right Panel ---
        self.right_panel_widget = QWidget()
        self.right_panel_layout = QVBoxLayout(self.right_panel_widget)
        self.main_splitter.addWidget(self.right_panel_widget)

        # Plotting Area (Right Panel)
        self.plot_stack = QStackedWidget()
        self.right_panel_layout.addWidget(self.plot_stack)

        # Plot Navigation and Layout Controls
        plot_nav_layout = QHBoxLayout()
        self.prev_page_button = QPushButton("Previous Plot")
        self.prev_page_button.clicked.connect(self._prev_page)
        plot_nav_layout.addWidget(self.prev_page_button)

        self.page_label = QLabel("Page 0/0")
        plot_nav_layout.addWidget(self.page_label)

        self.next_page_button = QPushButton("Next Plot")
        self.next_page_button.clicked.connect(self._next_page)
        plot_nav_layout.addWidget(self.next_page_button)

        plot_nav_layout.addStretch(1) # Add stretch to push layout combo to the right

        layout_label = QLabel("Plot Layout:")
        plot_nav_layout.addWidget(layout_label)
        self.layout_combo = QComboBox()
        self.layout_combo.addItems(self.layouts.keys())
        self.layout_combo.setCurrentText("2x2") # Set default to 2x2
        self.layout_combo.currentTextChanged.connect(self._render_plots)
        plot_nav_layout.addWidget(self.layout_combo)

        self.right_panel_layout.addLayout(plot_nav_layout)

        # Set initial sizes for the splitters - 优化布局比例
        # 左侧面板各部分：配置100px + 控制60px + 参数120px + 日志180px (压缩参数区域)
        self.left_panel_splitter.setSizes([100, 60, 120, 180])
        # 主分割器：左侧面板300px，右侧绘图区域1620px (更多空间给绘图)
        self.main_splitter.setSizes([300, 1620])

        self.all_plot_data = []
        self.current_page_index = 0
        self.pages = []
        self.manual_tuning_plot_data_index = -1
        self._plot_data_changed = False
        self._updating_gui = False

        self.extraction_completed_signal.connect(self._on_extraction_completed)
        self.extraction_failed_signal.connect(self._on_extraction_failed)
        self.enable_controls_signal.connect(self._enable_controls)
        self._update_page_navigation_buttons()
        sys.excepthook = self._handle_exception

        self.is_dark_theme = True # Default to dark theme
        if _config and 'GUI' in _config:
            if _config.get('GUI', 'theme', fallback='dark') == "light":
                self.is_dark_theme = False
        self._apply_theme()

        # 添加键盘快捷键
        self._setup_shortcuts()

    def _slider_pos_to_value(self, pos, key):
        min_val, max_val, scale = self.param_ranges[key]
        slider_min, slider_max = self.SLIDER_RANGE
        if scale == 'log':
            log_min, log_max = np.log10(min_val), np.log10(max_val)
            log_val = log_min + (log_max - log_min) * (pos / (slider_max - slider_min))
            return 10**log_val
        else:
            return min_val + (max_val - min_val) * (pos / (slider_max - slider_min))

    def _value_to_slider_pos(self, value, key):
        # Handle None values by returning minimum slider position
        if value is None:
            return self.SLIDER_RANGE[0]  # Return minimum slider position

        min_val, max_val, scale = self.param_ranges[key]
        slider_min, slider_max = self.SLIDER_RANGE
        value = max(min_val, min(value, max_val))
        if scale == 'log':
            log_min, log_max = np.log10(min_val), np.log10(max_val)
            log_val = np.log10(value)
            pos = (log_val - log_min) / (log_max - log_min) * (slider_max - slider_min)
            return int(pos)
        else:
            pos = (value - min_val) / (max_val - min_val) * (slider_max - slider_min)
            return int(pos)

    def _slider_value_changed(self, key, value):
        if self._updating_gui: return
        self.param_widgets[key]['line_edit'].editingFinished.disconnect()
        new_val = self._slider_pos_to_value(value, key)
        self.param_widgets[key]['line_edit'].setText(f"{new_val:.4e}")
        self.param_widgets[key]['line_edit'].editingFinished.connect(lambda k=key: self._line_edit_finished(k))
        if hasattr(self, '_update_timer'):
            self._update_timer.stop()
        self._update_timer = QTimer()
        self._update_timer.setSingleShot(True)
        self._update_timer.timeout.connect(self.update_plot)
        self._update_timer.start(150)

    def _line_edit_finished(self, key):
        if self._updating_gui: return
        self.param_widgets[key]['slider'].valueChanged.disconnect()

        text_input = self.param_widgets[key]['line_edit'].text().strip()

        # Handle "N/A" input (for parameters like BV when no breakdown detected)
        if text_input.upper() == "N/A":
            # Keep "N/A" text and set slider to minimum
            self.param_widgets[key]['slider'].setValue(self.SLIDER_RANGE[0])
        else:
            try:
                text_val = float(text_input)
                min_val, max_val, _ = self.param_ranges[key]
                clamped_val = max(min_val, min(text_val, max_val))
                if clamped_val != text_val:
                    self.param_widgets[key]['line_edit'].setText(f"{clamped_val:.4e}")
                new_pos = self._value_to_slider_pos(clamped_val, key)
                self.param_widgets[key]['slider'].setValue(new_pos)
            except ValueError:
                current_pos = self.param_widgets[key]['slider'].value()
                current_val = self._slider_pos_to_value(current_pos, key)
                self.param_widgets[key]['line_edit'].setText(f"{current_val:.4e}")

        self.param_widgets[key]['slider'].valueChanged.connect(lambda value, k=key: self._slider_value_changed(k, value))
        self.update_plot()

    def _setup_shortcuts(self):
        """设置键盘快捷键"""
        # F11: 全屏绘图
        QShortcut(QKeySequence("F11"), self, self._toggle_fullscreen_plot)

        # Ctrl+H: 隐藏/显示侧边栏
        QShortcut(QKeySequence("Ctrl+H"), self, self._toggle_sidebar)

        # Ctrl+T: 切换主题
        QShortcut(QKeySequence("Ctrl+T"), self, self._toggle_theme)

        # Ctrl+R: 运行提取
        QShortcut(QKeySequence("Ctrl+R"), self, self.run_extraction)

        # Ctrl+O: 打开数据文件
        QShortcut(QKeySequence("Ctrl+O"), self, self.browse_data_file)

        # 左右箭头: 切换图表页面
        QShortcut(QKeySequence("Left"), self, self._prev_page)
        QShortcut(QKeySequence("Right"), self, self._next_page)

        # Ctrl+Q: 退出
        QShortcut(QKeySequence("Ctrl+Q"), self, self.close)

    def _toggle_theme(self):
        self.is_dark_theme = not self.is_dark_theme
        self._apply_theme()

    def _toggle_sidebar(self):
        """切换侧边栏显示/隐藏"""
        if self.sidebar_visible:
            self.left_panel_splitter.hide()
            self.toggle_sidebar_button.setText("Show Sidebar")
            self.sidebar_visible = False
            # 给绘图区域更多空间
            self.main_splitter.setSizes([0, 1920])
        else:
            self.left_panel_splitter.show()
            self.toggle_sidebar_button.setText("Hide Sidebar")
            self.sidebar_visible = True
            # 恢复正常布局
            self.main_splitter.setSizes([300, 1620])

    def _toggle_fullscreen_plot(self):
        """切换全屏绘图模式"""
        if not self.plot_fullscreen:
            # 进入全屏模式
            self.showFullScreen()
            self.left_panel_splitter.hide()
            self.fullscreen_plot_button.setText("Exit Fullscreen")
            self.plot_fullscreen = True
            self.main_splitter.setSizes([0, self.width()])
        else:
            # 退出全屏模式
            self.showNormal()
            if self.sidebar_visible:
                self.left_panel_splitter.show()
            self.fullscreen_plot_button.setText("Fullscreen Plot")
            self.plot_fullscreen = False
            # 恢复正常布局
            if self.sidebar_visible:
                self.main_splitter.setSizes([300, 1620])
            else:
                self.main_splitter.setSizes([0, 1920])

    def _apply_theme(self):
        app = QApplication.instance()
        if self.is_dark_theme:
            plt.style.use('dark_background')
            plt.rcParams['grid.color'] = '#A9A9A9'
            plt.rcParams['grid.linestyle'] = '--'
            plt.rcParams['grid.linewidth'] = 0.5
            app.setStyleSheet(dark_stylesheet)
        else:
            plt.style.use('default')
            plt.rcParams['axes.facecolor'] = 'white' # Explicitly set plot background to white
            plt.rcParams['figure.facecolor'] = 'white' # Explicitly set figure background to white
            plt.rcParams['text.color'] = 'black' # Set text color to black
            plt.rcParams['axes.labelcolor'] = 'black' # Set axis label color to black
            plt.rcParams['xtick.color'] = 'black' # Set x-tick color to black
            plt.rcParams['ytick.color'] = 'black' # Set y-tick color to black
            plt.rcParams['grid.color'] = '#CCCCCC' # A lighter gray for light theme
            plt.rcParams['grid.linestyle'] = '-' # Solid line for light theme
            plt.rcParams['grid.linewidth'] = 0.8 # Slightly thicker for visibility
            app.setStyleSheet(light_stylesheet)
        self._render_plots()

        # Save theme preference
        if _config:
            try:
                _config.set('GUI', 'theme', "dark" if self.is_dark_theme else "light")
                with open('config.ini', 'w') as configfile:
                    _config.write(configfile)
            except Exception as e:
                logging.warning(f"Could not save theme preference: {e}")


    def _calculate_r_squared(self, y_true, y_pred):
        if len(y_true) == 0 or len(y_true) != len(y_pred): return np.nan
        ss_res = np.sum((y_true - y_pred) ** 2)
        ss_tot = np.sum((y_true - np.mean(y_true)) ** 2)
        if ss_tot == 0: return 1.0 if ss_res == 0 else 0.0
        return 1 - (ss_res / ss_tot)

    def _update_r_squared_label(self):
        if self.manual_tuning_plot_data_index != -1 and self.all_plot_data:
            plot_data = self.all_plot_data[self.manual_tuning_plot_data_index]
            model = plot_data['model']
            v_ca_a, i_c_a_measured = plot_data['v_ca_a'], plot_data['i_c_a']
            i_c_a_model = model.calc_ic_diode_ohmic_a(v_ca_a)
            v_ca_cropped, i_c_a_measured_cropped = crop_data_range_to_x(v_ca_a, i_c_a_measured, model.vca_lim_lower_ic, model.vca_lim_upper_ic)
            _, i_c_a_model_cropped = crop_data_range_to_x(v_ca_a, i_c_a_model, model.vca_lim_lower_ic, model.vca_lim_upper_ic)
            r_squared = self._calculate_r_squared(i_c_a_measured_cropped, i_c_a_model_cropped)
            self.r_squared_label.setText(f"R² (I-V Fit): {r_squared:.4f}" if not np.isnan(r_squared) else "R² (I-V Fit): N/A")

    def _handle_exception(self, exc_type, exc_value, exc_traceback):
        if issubclass(exc_type, KeyboardInterrupt):
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        error_message = f"Unhandled exception:\n{exc_type.__name__}: {exc_value}\n{''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))}"
        self.update_log_signal.emit(error_message)
        logging.critical("An unhandled error occurred. The application may be unstable. Please check the Output Log for details and consider restarting.")
        self.statusBar.showMessage("Unhandled exception occurred. Check log.")
        self.enable_controls_signal.emit()

    def _add_file_selector(self, layout, label_text, line_edit_widget, browse_func):
        h_layout = QHBoxLayout()
        h_layout.addWidget(QLabel(label_text))
        h_layout.addWidget(line_edit_widget)
        if browse_func:
            browse_button = QPushButton("Browse")
            browse_button.clicked.connect(browse_func)
            h_layout.addWidget(browse_button)
        layout.addLayout(h_layout)

    def _add_test_data_selector(self):
        """Add test data selector"""
        h_layout = QHBoxLayout()
        label = QLabel("Test Data Selection:")
        label.setMinimumWidth(120)
        h_layout.addWidget(label)

        self.test_data_combo = QComboBox()
        self.test_data_combo.addItems([
            "All Data",
            "Nominal Temperature Only",
            "Custom Selection"
        ])
        self.test_data_combo.setCurrentIndex(0)  # Default to all data
        self.test_data_combo.currentTextChanged.connect(self._on_test_data_selection_changed)
        h_layout.addWidget(self.test_data_combo)

        # Custom selection checkboxes (initially hidden)
        self.custom_data_widget = QWidget()
        self.custom_data_layout = QHBoxLayout(self.custom_data_widget)
        self.custom_data_layout.setContentsMargins(0, 0, 0, 0)
        self.custom_data_widget.hide()

        self.config_layout.addLayout(h_layout)
        self.config_layout.addWidget(self.custom_data_widget)

    def _on_test_data_selection_changed(self, text):
        """Handle test data selection change"""
        if "Custom Selection" in text:
            self.custom_data_widget.show()
            self._load_available_test_data()
        else:
            self.custom_data_widget.hide()

    def _load_available_test_data(self):
        """Load available test data options"""
        # Clear existing checkboxes
        for i in reversed(range(self.custom_data_layout.count())):
            child = self.custom_data_layout.itemAt(i).widget()
            if child:
                child.deleteLater()

        # Try to read data file to get available test data
        try:
            import json
            with open(self.data_file_path.text(), 'r') as f:
                data = json.load(f)

            self.test_data_checkboxes = {}
            for test_name in data.keys():
                checkbox = QCheckBox(test_name)
                checkbox.setChecked(True)  # Default to all selected
                self.test_data_checkboxes[test_name] = checkbox
                self.custom_data_layout.addWidget(checkbox)

        except Exception as e:
            # If unable to read file, show default options
            default_tests = ["T298.0K", "T323.0K", "T348.0K", "T373.0K", "T398.0K", "T423.0K"]
            self.test_data_checkboxes = {}
            for test_name in default_tests:
                checkbox = QCheckBox(test_name)
                checkbox.setChecked(True)
                self.test_data_checkboxes[test_name] = checkbox
                self.custom_data_layout.addWidget(checkbox)

    def _get_selected_test_data(self):
        """Get user selected test data"""
        selection_text = self.test_data_combo.currentText()

        if "All Data" in selection_text:
            return None  # None means process all data
        elif "Nominal Temperature Only" in selection_text:
            return "nominal_only"  # Special marker for nominal temperature data only
        elif "Custom Selection" in selection_text:
            # Return list of selected test data
            if hasattr(self, 'test_data_checkboxes'):
                selected = []
                for test_name, checkbox in self.test_data_checkboxes.items():
                    if checkbox.isChecked():
                        selected.append(test_name)
                return selected if selected else None
            return None
        else:
            return None

    def _add_parameter_widget(self, key, min_val, max_val, scale, initial_value=None):
        row = self.params_layout.rowCount()

        # 创建标签
        label = QLabel(key)
        label.setMinimumWidth(50)
        label.setMaximumWidth(80)

        # 创建更大的输入框，让数字更清晰
        line_edit = QLineEdit()
        line_edit.setMinimumWidth(120)
        line_edit.setMaximumWidth(150)
        line_edit.setMinimumHeight(25)  # 增加高度让文字更清晰
        if initial_value is not None:
            line_edit.setText(f"{initial_value:.4e}")
        else:
            # Set a default value to prevent empty text
            default_val = (min_val + max_val) / 2 if scale == 'linear' else np.sqrt(min_val * max_val)
            line_edit.setText(f"{default_val:.4e}")
            initial_value = default_val

        # 创建紧凑的滑块
        slider = QSlider(Qt.Horizontal)
        slider.setRange(self.SLIDER_RANGE[0], self.SLIDER_RANGE[1])
        slider.setMinimumWidth(100)
        slider.setMaximumWidth(150)

        self.params_layout.addWidget(label, row, 0)
        self.params_layout.addWidget(line_edit, row, 1)
        self.params_layout.addWidget(slider, row, 2)

        self.param_widgets[key] = {
            'label': label,
            'line_edit': line_edit,
            'slider': slider,
            'min_val': min_val,
            'max_val': max_val,
            'scale': scale
        }

        # Add to param_keys only if not already present
        if key not in self.param_keys:
            self.param_keys.append(key)
        self.param_ranges[key] = (min_val, max_val, scale)

        # Set initial slider value before connecting signals
        if initial_value is not None:
            slider.setValue(self._value_to_slider_pos(initial_value, key))

        # Connect signals after setting initial values
        line_edit.editingFinished.connect(lambda k=key: self._line_edit_finished(k))
        slider.valueChanged.connect(lambda value, k=key: self._slider_value_changed(k, value))

    def _remove_parameter_widget(self, key):
        if key in self.param_widgets:
            widgets = self.param_widgets[key]
            self.params_layout.removeWidget(widgets['label'])
            self.params_layout.removeWidget(widgets['line_edit'])
            self.params_layout.removeWidget(widgets['slider'])
            widgets['label'].deleteLater()
            widgets['line_edit'].deleteLater()
            widgets['slider'].deleteLater()
            del self.param_widgets[key]
            self.param_keys.remove(key)
            # Don't delete from param_ranges - keep it for future re-adding
            # del self.param_ranges[key]  # Commented out to allow re-adding parameters
            self._rearrange_param_layout()

    def _rearrange_param_layout(self):
        # Clear existing layout
        for i in reversed(range(self.params_layout.count())):
            widget = self.params_layout.itemAt(i).widget()
            if widget:
                widget.setParent(None)

        # Re-add widgets based on updated param_keys order
        for row, key in enumerate(self.param_keys):
            widgets = self.param_widgets[key]
            self.params_layout.addWidget(widgets['label'], row, 0)
            self.params_layout.addWidget(widgets['line_edit'], row, 1)
            self.params_layout.addWidget(widgets['slider'], row, 2)
        
        # Re-add R^2 label and Reset button at the end
        self.params_layout.addWidget(self.r_squared_label, len(self.param_keys), 0, 1, 2)
        self.params_layout.addWidget(self.reset_params_button, len(self.param_keys), 2)

    def _add_parameter_dialog(self):
        """Show dialog to add parameter from extracted model parameters"""
        if not hasattr(self, 'initial_extracted_params') or not self.initial_extracted_params:
            QMessageBox.information(self, "No Parameters Available",
                                  "Please run parameter extraction first to get available parameters.")
            return

        dialog = QDialog(self)
        dialog.setWindowTitle("Add Parameter from Model")
        dialog.setMinimumWidth(400)
        dialog_layout = QVBoxLayout(dialog)

        # Information label
        info_label = QLabel("Select a parameter from the extracted model to add to the tuning panel:")
        dialog_layout.addWidget(info_label)

        # Get available parameters (not currently displayed)
        available_params = []

        # Check all possible HSPICE Level=3 parameters
        for param_name in self.param_ranges.keys():
            if param_name not in self.param_keys:
                # Get value from extracted params if available, otherwise use None
                param_value = self.initial_extracted_params.get(param_name, None)
                available_params.append((param_name, param_value))

        if not available_params:
            QMessageBox.information(self, "No Parameters Available",
                                  "All available HSPICE Level=3 parameters are already displayed in the tuning panel.")
            return

        # Parameter selection
        param_combo = QComboBox()
        for param_name, param_value in available_params:
            display_text = f"{param_name}: {param_value:.4e}" if param_value is not None else f"{param_name}: N/A"
            param_combo.addItem(display_text, param_name)

        form_layout = QFormLayout()
        form_layout.addRow("Available Parameters:", param_combo)
        dialog_layout.addLayout(form_layout)

        # Show parameter info
        info_text = QLabel()
        info_text.setWordWrap(True)
        info_text.setStyleSheet("QLabel { background-color: #f0f0f0; padding: 8px; border-radius: 4px; }")
        dialog_layout.addWidget(info_text)

        def update_param_info():
            selected_param = param_combo.currentData()
            if selected_param in self.param_ranges:
                min_val, max_val, scale = self.param_ranges[selected_param]
                param_descriptions = {
                    "IS": "Saturation current - Controls the exponential current behavior",
                    "N": "Ideality factor - Controls the slope of the I-V curve",
                    "RS": "Series resistance - Causes voltage drop at high currents",
                    "CJO": "Zero-bias junction capacitance - Base capacitance value",
                    "VJ": "Junction potential - Built-in voltage of the junction",
                    "M": "Grading coefficient - Controls capacitance voltage dependence",
                    "FC": "Forward-bias capacitance coefficient - Transition point for capacitance model",
                    "BV": "Breakdown voltage - Reverse voltage where breakdown occurs",
                    "IBV": "Breakdown current - Current at breakdown voltage",
                    "TT": "Transit time - Controls diffusion capacitance",
                    "EG": "Energy gap - Bandgap energy for temperature dependence",
                    "XTI": "Saturation current temperature exponent - Temperature coefficient"
                }
                desc = param_descriptions.get(selected_param, "HSPICE Level=3 diode parameter")
                info_text.setText(f"Parameter: {selected_param}\n"
                                f"Description: {desc}\n"
                                f"Range: {min_val:.2e} to {max_val:.2e} ({scale} scale)")

        param_combo.currentTextChanged.connect(update_param_info)
        update_param_info()  # Initial update

        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel, Qt.Horizontal, dialog)
        buttons.accepted.connect(dialog.accept)
        buttons.rejected.connect(dialog.reject)
        dialog_layout.addWidget(buttons)

        if dialog.exec_() == QDialog.Accepted:
            selected_param = param_combo.currentData()
            if selected_param:
                min_val, max_val, scale = self.param_ranges[selected_param]
                initial_value = self.initial_extracted_params.get(selected_param)
                if initial_value is None:
                    initial_value = (min_val + max_val) / 2  # Use middle value for None

                self._add_parameter_widget(selected_param, min_val, max_val, scale, initial_value)
                logging.info(f"Added parameter: {selected_param} = {initial_value}")
                QMessageBox.information(self, "Parameter Added",
                                      f"Parameter '{selected_param}' has been added to the tuning panel.")

    def _remove_parameter_dialog(self):
        dialog = QDialog(self)
        dialog.setWindowTitle("Remove Parameter")
        dialog_layout = QVBoxLayout(dialog)

        param_combo = QComboBox()
        param_combo.addItems(self.param_keys)
        dialog_layout.addWidget(QLabel("Select Parameter to Remove:"))
        dialog_layout.addWidget(param_combo)

        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel, Qt.Horizontal, dialog)
        buttons.accepted.connect(dialog.accept)
        buttons.rejected.connect(dialog.reject)
        dialog_layout.addWidget(buttons)

        if dialog.exec_() == QDialog.Accepted:
            selected_param = param_combo.currentText()
            if selected_param:
                self._remove_parameter_widget(selected_param)
                logging.info(f"Removed parameter: {selected_param}")
            else:
                logging.warning("No parameter selected for removal.")

    def append_text(self, text):
        self.output_text.moveCursor(self.output_text.textCursor().End)
        self.output_text.insertPlainText(text)
        # Update status bar with the last line of the log
        last_line = text.strip().split('\n')[-1]
        if last_line:
            self.statusBar.showMessage(last_line)

    def browse_data_file(self):
        file_path, _ = QFileDialog.getOpenFileName(self, "Select Measurement Data File", os.getcwd(), "Measurement files (*.json *.dat);;JSON files (*.json);;DAT files (*.dat);;All files (*.*)")
        if file_path: self.data_file_path.setText(file_path)

    def browse_plot_dir(self):
        dir_path = QFileDialog.getExistingDirectory(self, "Select Plots Directory", os.getcwd())
        if dir_path: self.plot_dir_path.setText(dir_path)

    def run_extraction(self):
        data_file = self.data_file_path.text()
        if not data_file:
            logging.warning("Input Error: Please select a measurement data file.")
            self.statusBar.showMessage("Input Error: Please select a measurement data file.")
            return

        self.output_text.clear()
        self.update_log_signal.emit("Starting diode parameter extraction... This may take a moment.\n")
        self.progress_bar.setValue(0) # Reset progress bar
        self.run_button.setEnabled(False)
        for key in self.param_keys:
            if key in self.param_widgets:
                self.param_widgets[key]['slider'].setEnabled(False)
                self.param_widgets[key]['line_edit'].setEnabled(False)
        self.all_plot_data = []
        self._plot_data_changed = True
        self.current_page_index = 0
        for i in reversed(range(self.plot_stack.count())):
            widget_to_remove = self.plot_stack.widget(i)
            self.plot_stack.removeWidget(widget_to_remove)
            widget_to_remove.deleteLater()
        self.pages = []
        self._update_page_navigation_buttons()

        # 获取用户选择的测试数据
        selected_test_data = self._get_selected_test_data()

        self.thread = QThread()
        self.worker = Worker(data_file, self.results_file_path.text(), self.plot_dir_path.text(), selected_test_data)
        self.worker.moveToThread(self.thread)

        self.thread.started.connect(self.worker.run)
        self.worker.completed.connect(self._on_extraction_completed)
        self.worker.failed.connect(self._on_extraction_failed)
        self.worker.progress_value.connect(self.progress_bar.setValue)
        self.worker.completed.connect(self.thread.quit)
        self.worker.failed.connect(self.thread.quit)
        self.thread.finished.connect(self.thread.deleteLater)
        self.worker.completed.connect(lambda: self.enable_controls_signal.emit())
        self.worker.failed.connect(lambda: self.enable_controls_signal.emit())

        self.thread.start()

    

    def _on_extraction_completed(self, plot_data_list, extracted_params, manual_tuning_plot_data_index):
        self._updating_gui = True # Prevent update_plot from being called during programmatic updates
        self.all_plot_data = plot_data_list
        self.initial_extracted_params = extracted_params # Store initial extracted parameters
        self.manual_tuning_plot_data_index = manual_tuning_plot_data_index
        self._plot_data_changed = True
        # Initialize all parameters to 0.0 to prevent empty strings
        for key in list(self.param_widgets.keys()): # Iterate over a copy of keys as dict might change size
            self.param_widgets[key]['line_edit'].setText("0.0")
            self.param_widgets[key]['slider'].setValue(self._value_to_slider_pos(0.0, key)) # Set slider to 0.0 position

        for key, value in extracted_params.items():
            if key in self.param_widgets:
                # Handle None values (e.g., BV when no breakdown detected)
                if value is None:
                    self.param_widgets[key]['line_edit'].setText("N/A")
                    self.param_widgets[key]['slider'].setValue(self._value_to_slider_pos(0.0, key))  # Set slider to minimum
                else:
                    self.param_widgets[key]['line_edit'].setText(f"{value:.4e}")
                    self.param_widgets[key]['slider'].setValue(self._value_to_slider_pos(value, key))
            else:
                # Add new parameter if it exists in extracted_params but not in GUI
                # Use default min/max/scale for newly added parameters if not explicitly defined
                min_val = 1e-15 # Default min for new parameters
                max_val = 1e-6  # Default max for new parameters
                scale = 'log'   # Default scale for new parameters
                self._add_parameter_widget(key, min_val, max_val, scale, initial_value=value)
        self._update_r_squared_label()
        self._render_plots()
        self.update_log_signal.emit(f"\nExtraction complete! Check '{self.results_file_path.text()}' for results and '{self.plot_dir_path.text()}' for plots.\n")
        logging.info("Diode parameter extraction completed successfully!")
        self.progress_bar.setValue(100) # Set progress to 100% on completion
        self.statusBar.showMessage("Extraction completed successfully!")
        self._updating_gui = False # Re-enable update_plot for user interactions

    def _on_extraction_failed(self, error_message):
        self.update_log_signal.emit(f"\nERROR: {error_message}\n")
        logging.critical("An error occurred during diode parameter extraction. Please check the Output Log for details.")
        self.progress_bar.setValue(0) # Reset progress to 0% on failure

    def _enable_controls(self):
        self.run_button.setEnabled(True)
        for key in self.param_keys:
            if key in self.param_widgets:
                self.param_widgets[key]['slider'].setEnabled(True)
                self.param_widgets[key]['line_edit'].setEnabled(True)

    def _render_plots(self):
        self.update_log_signal.emit("Rendering plots...\n")
        try:
            # Clear existing plots only if data has changed or layout has changed
            if self._plot_data_changed or \
               not hasattr(self, '_last_layout') or self.layout_combo.currentText() != self._last_layout:
                for i in reversed(range(self.plot_stack.count())):
                    widget_to_remove = self.plot_stack.widget(i)
                    self.plot_stack.removeWidget(widget_to_remove)
                    widget_to_remove.deleteLater()
                self.pages = []
                self._plot_data_changed = False
                self._last_layout = self.layout_combo.currentText()

            if not self.all_plot_data:
                self.page_label.setText("Page 0/0")
                self._update_page_navigation_buttons()
                return

            rows, cols = self.layouts[self.layout_combo.currentText()]
            plots_per_page = rows * cols
            current_plot_data_index = 0

            # If pages are not yet created, create them
            if not self.pages:
                while current_plot_data_index < len(self.all_plot_data):
                    page_widget = QWidget()
                    page_layout = QGridLayout(page_widget)
                    # 增大图表尺寸，适应更大的绘图区域
                    figure_width = cols * 6 if cols <= 2 else cols * 5
                    figure_height = rows * 4.5 if rows <= 2 else rows * 4
                    page_figure = Figure(figsize=(figure_width, figure_height), dpi=100)
                    if self.is_dark_theme:
                        page_figure.set_facecolor('#2E2E2E')
                    else:
                        page_figure.set_facecolor('white')
                    page_canvas = FigureCanvas(page_figure)
                    page_layout.addWidget(page_canvas, 0, 0, rows, cols)
                    toolbar = NavigationToolbar(page_canvas, page_widget)
                    page_layout.addWidget(toolbar, rows, 0, 1, cols)
                    axes_on_page = []
                    for r in range(rows):
                        for c in range(cols):
                            if current_plot_data_index < len(self.all_plot_data):
                                ax = page_figure.add_subplot(rows, cols, len(axes_on_page) + 1)
                                if self.is_dark_theme:
                                    ax.set_facecolor('#2E2E2E')
                                else:
                                    ax.set_facecolor('white')
                                axes_on_page.append(ax)
                                ax.grid(True)
                                page_canvas.mpl_connect('button_press_event', lambda event, current_ax=ax: self._on_plot_right_click(event, current_ax))
                                current_plot_data_index += 1
                            else: break
                    page_figure.tight_layout()
                    self.pages.append(page_widget)
                    self.plot_stack.addWidget(page_widget)
            
            # Update existing plots
            current_plot_data_index = 0
            for page_idx, page_widget in enumerate(self.pages):
                if self.is_dark_theme:
                    page_widget.setStyleSheet("background-color: #2E2E2E;")
                else:
                    page_widget.setStyleSheet("background-color: white;")
                page_figure = page_widget.findChild(FigureCanvas).figure
                if self.is_dark_theme:
                    page_figure.set_facecolor('#2E2E2E')
                else:
                    page_figure.set_facecolor('white')
                page_canvas = page_widget.findChild(FigureCanvas)
                if self.is_dark_theme:
                    page_canvas.setStyleSheet("background-color: #2E2E2E;")
                else:
                    page_canvas.setStyleSheet("background-color: white;")
                for ax in page_figure.get_axes():
                    ax.clear() # Clear existing plot content
                    if self.is_dark_theme:
                        ax.set_facecolor('#2E2E2E')
                    else:
                        ax.set_facecolor('white')
                    if current_plot_data_index < len(self.all_plot_data):
                        plot_data = self.all_plot_data[current_plot_data_index]
                        ax.plot_data = plot_data # Store plot_data on ax for right-click context
                        plot_type = plot_data['plot_type']
                        # Only render the simple IV plots
                        if plot_type == 'iv_data_only':
                            # Show only measured data
                            self._plot_iv_data_only(plot_data, ax)
                        elif plot_type == 'iv_data_with_model':
                            # Show measured data + model fitting
                            self._plot_iv_data_with_model(plot_data, ax)
                            if current_plot_data_index == self.manual_tuning_plot_data_index:
                                # Re-initialize SpanSelector for manual tuning
                                if hasattr(self, 'span') and self.span.ax == ax:
                                    self.span.clear()
                                self.span = SpanSelector(ax, self._on_span_select, 'horizontal', useblit=True)
                                self.span.extents = (plot_data['model'].vca_lim_lower_ic, plot_data['model'].vca_lim_upper_ic)
                                self.span.active = True
                        else:
                            # Hide all other plot types
                            ax.set_visible(False)
                        current_plot_data_index += 1
                    else:
                        ax.set_visible(False) # Hide unused subplots
                page_figure.canvas.draw_idle() # Redraw the canvas
            
            self.plot_stack.setCurrentIndex(self.current_page_index)
            self._update_page_navigation_buttons()
            self.update_log_signal.emit("Plots rendered successfully.\n")
        except Exception as e:
            error_message = f"Error during plot rendering:\n{e}\n{''.join(traceback.format_exc())}"
            self.update_log_signal.emit(error_message)
            logging.critical("An error occurred during plot rendering. Please check the Output Log for details.")
            self.statusBar.showMessage("Plotting error. Check log.")

    def _plot_iv_data_only(self, plot_data, ax):
        """Plot only measured IV data."""
        try:
            v_ca_a = plot_data['v_ca_a']
            i_c_a = plot_data['i_c_a']
            temp_c = plot_data.get('temperature_C', 25.0)
            measurement_key = plot_data.get('measurement_key', 'Unknown')

            # Clear the axis
            ax.clear()

            # Plot only measured data
            ax.plot(v_ca_a, i_c_a, 'bo-', linewidth=2, markersize=4, label='Measured Data')
            ax.set_xlabel('Voltage (V)')
            ax.set_ylabel('Current (A)')
            ax.set_title(f'IV Data - {measurement_key} (T={temp_c}°C)')
            ax.grid(True, alpha=0.3)
            ax.legend()

            # Set appropriate axis limits
            v_margin = (max(v_ca_a) - min(v_ca_a)) * 0.05
            i_margin = (max(i_c_a) - min(i_c_a)) * 0.05
            ax.set_xlim(min(v_ca_a) - v_margin, max(v_ca_a) + v_margin)
            ax.set_ylim(min(i_c_a) - i_margin, max(i_c_a) + i_margin)

        except Exception as e:
            ax.text(0.5, 0.5, f'Error plotting IV data:\n{str(e)}',
                   ha='center', va='center', transform=ax.transAxes)

    def _plot_iv_data_with_model(self, plot_data, ax):
        """Plot measured IV data with model fitting."""
        try:
            v_ca_a = plot_data['v_ca_a']
            i_c_a = plot_data['i_c_a']
            model = plot_data['model']
            temp_c = plot_data.get('temperature_C', 25.0)
            measurement_key = plot_data.get('measurement_key', 'Unknown')

            # Clear the axis
            ax.clear()

            # Plot measured data
            ax.plot(v_ca_a, i_c_a, 'bo-', linewidth=2, markersize=4, label='Measured Data')

            # Plot model fitting
            v_model = np.linspace(min(v_ca_a), max(v_ca_a), 200)
            i_model = model.i_c_model(v_model)
            ax.plot(v_model, i_model, 'r-', linewidth=2, label='Model Fit')

            ax.set_xlabel('Voltage (V)')
            ax.set_ylabel('Current (A)')
            ax.set_title(f'IV Data + Model Fit - {measurement_key} (T={temp_c}°C)')
            ax.grid(True, alpha=0.3)
            ax.legend()

            # Set appropriate axis limits
            v_margin = (max(v_ca_a) - min(v_ca_a)) * 0.05
            i_margin = (max(i_c_a) - min(i_c_a)) * 0.05
            ax.set_xlim(min(v_ca_a) - v_margin, max(v_ca_a) + v_margin)
            ax.set_ylim(min(i_c_a) - i_margin, max(i_c_a) + i_margin)

        except Exception as e:
            ax.text(0.5, 0.5, f'Error plotting IV data with model:\n{str(e)}',
                   ha='center', va='center', transform=ax.transAxes)

    def _update_page_navigation_buttons(self):
        total_pages = len(self.pages)
        self.page_label.setText(f"Page {self.current_page_index + 1}/{total_pages}")
        self.prev_page_button.setEnabled(self.current_page_index > 0)
        self.next_page_button.setEnabled(self.current_page_index < total_pages - 1)

    def _prev_page(self):
        if self.current_page_index > 0:
            self.current_page_index -= 1
            self.plot_stack.setCurrentIndex(self.current_page_index)
            self._update_page_navigation_buttons()

    def _next_page(self):
        if self.current_page_index < len(self.pages) - 1:
            self.current_page_index += 1
            self.plot_stack.setCurrentIndex(self.current_page_index)
            self._update_page_navigation_buttons()

    def update_plot(self):
        self.update_log_signal.emit("Attempting to update plot...\n")
        try:
            if self.manual_tuning_plot_data_index == -1 or not self.all_plot_data: return

            # Build parameters dictionary, handling "N/A" values
            params = {}
            for key in self.param_keys:
                if key in self.param_widgets:
                    text_val = self.param_widgets[key]['line_edit'].text().strip()
                    if text_val.upper() == "N/A":
                        params[key] = None  # Use None for N/A values
                    else:
                        try:
                            params[key] = float(text_val)
                        except ValueError:
                            # Skip invalid values
                            continue

            self.update_log_signal.emit(f"Updating model with: {', '.join([f'{k}={v:.2e}' if v is not None else f'{k}=N/A' for k, v in params.items()])}\n")
            manual_tuning_plot_data = self.all_plot_data[self.manual_tuning_plot_data_index]
            manual_tuning_model = manual_tuning_plot_data['model']
            manual_tuning_model.set_parameters(**params)
            self._update_r_squared_label()
            self._render_plots()
            self.update_log_signal.emit("Plot updated successfully.\n")
        except Exception as e:
            error_message = f"""Error during manual plot update:
{e}
{''.join(traceback.format_exc())}"""
            self.update_log_signal.emit(error_message)
            logging.critical("An error occurred during manual tuning. Please check the Output Log for details.")
            self.statusBar.showMessage("Manual tuning error. Check log.")

    def view_plots(self):
        plot_dir = self.plot_dir_path.text()
        if not os.path.exists(plot_dir):
            logging.warning(f"Plots directory '{plot_dir}' does not exist yet.")
            self.statusBar.showMessage(f"Warning: Plots directory '{plot_dir}' does not exist yet.")
            return
        try:
            if sys.platform == "win32": os.startfile(plot_dir)
            elif sys.platform == "darwin": os.system(f"open {plot_dir}")
            else: os.system(f"xdg-open {plot_dir}")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Could not open plots directory: {e}")

    def _set_x_scale(self, ax, scale_type):
        try:
            if scale_type == 'log' and (np.any(np.array(ax.get_xlim()) <= 0) or np.any(ax.get_lines()[0].get_xdata() <= 0)):
                logging.warning("Cannot set X-axis to log scale: data contains non-positive values.")
                self.statusBar.showMessage("Log Scale Error: X-axis data contains non-positive values.")
                return
            ax.set_xscale(scale_type, linthresh=1e-9 if scale_type == 'symlog' else None)
            ax.figure.canvas.draw_idle()
        except Exception as e:
            logging.error(f"Error setting X-axis scale: {e}")
            self.statusBar.showMessage(f"Error setting X-axis scale: {e}")

    def _set_y_scale(self, ax, scale_type):
        try:
            if scale_type == 'log' and (np.any(np.array(ax.get_ylim()) <= 0) or np.any(ax.get_lines()[0].get_ydata() <= 0)):
                logging.warning("Cannot set Y-axis to log scale: data contains non-positive values.")
                self.statusBar.showMessage("Log Scale Error: Y-axis data contains non-positive values.")
                return
            ax.set_yscale(scale_type, linthresh=1e-9 if scale_type == 'symlog' else None)
            ax.figure.canvas.draw_idle()
        except Exception as e:
            logging.error(f"Error setting Y-axis scale: {e}")
            self.statusBar.showMessage(f"Error setting Y-axis scale: {e}")

    def _on_plot_right_click(self, event, ax):
        if event.button == 3:
            menu = QMenu(self)
            actions = {
                "X-Axis: Linear": lambda: self._set_x_scale(ax, 'linear'),
                "X-Axis: Log": lambda: self._set_x_scale(ax, 'log'),
                "X-Axis: Symmetric Log": lambda: self._set_x_scale(ax, 'symlog'),
                "Y-Axis: Linear": lambda: self._set_y_scale(ax, 'linear'),
                "Y-Axis: Log": lambda: self._set_y_scale(ax, 'log'),
                "Y-Axis: Symmetric Log": lambda: self._set_y_scale(ax, 'symlog'),
                "Calculate Differential Resistance": lambda: self._calculate_differential_resistance_for_ax(ax),
                "Reset View": lambda: ax.autoscale_view()
            }
            for title, func in actions.items():
                if "Y-Axis" in title or "Reset" in title: menu.addSeparator()
                menu.addAction(title).triggered.connect(func)
            menu.exec_(self.mapToGlobal(event.guiEvent.globalPos()))
            ax.figure.canvas.draw_idle()

    def _on_span_select(self, xmin, xmax):
        self.update_log_signal.emit(f"Span selected: {xmin:.4f}V to {xmax:.4f}V\n")
        try:
            if self.manual_tuning_plot_data_index == -1 or not self.all_plot_data: return
            plot_data = self.all_plot_data[self.manual_tuning_plot_data_index]
            model, v_ca_a, i_c_a, T = plot_data['model'], plot_data['v_ca_a'], plot_data['i_c_a'], plot_data['model'].T
            new_i_s, new_m = ideal_diode_model(v_ca_a, i_c_a, xmin, xmax, T)
            
            for k in ["I_S", "m"]:
                if k in self.param_widgets:
                    self.param_widgets[k]['slider'].valueChanged.disconnect()
                    self.param_widgets[k]['line_edit'].editingFinished.disconnect()

            if "I_S" in self.param_widgets:
                self.param_widgets["I_S"]['line_edit'].setText(f"{new_i_s:.4e}")
                self.param_widgets["I_S"]['slider'].setValue(self._value_to_slider_pos(new_i_s, "I_S"))
            if "m" in self.param_widgets:
                self.param_widgets["m"]['line_edit'].setText(f"{new_m:.4e}")
                self.param_widgets["m"]['slider'].setValue(self._value_to_slider_pos(new_m, "m"))

            for k in ["I_S", "m"]:
                if k in self.param_widgets:
                    self.param_widgets[k]['slider'].valueChanged.connect(lambda value, key=k: self._slider_value_changed(key, value))
                    self.param_widgets[k]['line_edit'].editingFinished.connect(lambda key=k: self._line_edit_finished(key))

            model.params['I_S'] = new_i_s
            model.params['m'] = new_m
            model.vca_lim_lower_ic, model.vca_lim_upper_ic = xmin, xmax
            self._update_r_squared_label()
            self._render_plots()
            self.update_log_signal.emit("Model parameters updated from span selection.\n")
        except Exception as e:
            error_message = f"""Error during span selection update:
{e}
{''.join(traceback.format_exc())}"""
            self.update_log_signal.emit(error_message)
            logging.critical("An error occurred during span selection. Please check the Output Log for details.")
            self.statusBar.showMessage("Span selection error. Check log.")

    def _calculate_differential_resistance_for_ax(self, ax):
        self.update_log_signal.emit("Calculating differential resistance for selected plot...\n")
        try:
            plot_data = getattr(ax, 'plot_data', None)
            if not plot_data or plot_data['plot_type'] not in ['vca_ic', 'vca_ic_ideal', 'vca_ic_r']:
                self.update_log_signal.emit("Differential resistance calculation not supported for this plot type.\n")
                return
            v_ca_a, i_c_a = plot_data['v_ca_a'], plot_data['i_c_a']
            dI, dV = np.diff(i_c_a), np.diff(v_ca_a)
            valid_indices = np.abs(dI) > 1e-12
            if np.any(valid_indices):
                r_diff, v_mid = dV[valid_indices] / dI[valid_indices], (v_ca_a[:-1] + v_ca_a[1:])[valid_indices] / 2
                diff_res_fig = Figure(figsize=(8, 6))
                diff_res_canvas = FigureCanvas(diff_res_fig)
                diff_res_ax = diff_res_fig.add_subplot(111)
                diff_res_ax.plot(v_mid, r_diff, 'b-', label='Differential Resistance (dV/dI)')
                diff_res_ax.set_xlabel('Voltage (V)'), diff_res_ax.set_ylabel('Differential Resistance (Ohm)')
                diff_res_ax.set_title('Differential Resistance vs. Voltage'), diff_res_ax.grid(True), diff_res_ax.legend()
                diff_res_fig.tight_layout()
                diff_res_window = QMainWindow(self)
                diff_res_window.setWindowTitle("Differential Resistance"), diff_res_window.setCentralWidget(FigureCanvas(diff_res_fig))
                diff_res_window.show()
                self.update_log_signal.emit("Differential resistance calculation complete.\n")
            else:
                self.update_log_signal.emit("No valid data points for differential resistance calculation (dI is too small).\n")
        except Exception as e:
            error_message = f"""Error during differential resistance calculation:
{e}
{''.join(traceback.format_exc())}"""
            self.update_log_signal.emit(error_message)
            logging.critical("An error occurred during differential resistance calculation. Please check the Output Log for details.")
            logging.critical("An error occurred during differential resistance calculation. Please check the Output Log for details.")
            self.statusBar.showMessage("Differential resistance calculation error. Check log.")

    def _reset_parameters(self):
        if hasattr(self, 'initial_extracted_params') and self.initial_extracted_params:
            for key, value in self.initial_extracted_params.items():
                if key in self.param_widgets:
                    self.param_widgets[key]['line_edit'].setText(f"{value:.4e}")
                    self.param_widgets[key]['slider'].setValue(self._value_to_slider_pos(value, key))
                else:
                    # If the parameter was not initially present, add it
                    min_val = 1e-15 # Default min for new parameters
                    max_val = 1e-6  # Default max for new parameters
                    scale = 'log'   # Default scale for new parameters
                    self._add_parameter_widget(key, min_val, max_val, scale, initial_value=value)
            self.update_log_signal.emit("Parameters reset to initially extracted values.\n")
            self.update_plot()
        else:
            self.update_log_signal.emit("No initial parameters to reset to. Please run extraction first.\n")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    window = DiodeParameterExtractorGUI()
    window.show()
    sys.exit(app.exec_())