import numpy as np

""" General helper functions to handle measurement data
"""

def crop_data_range_to_x(xdata, ydata, lower, upper):
    """Crop two data vectors so that the second corresponds to the first.

    This implementation uses NumPy for efficiency.

    Args:
        xdata (1D array-like):
        ydata (1D array-like):
        lower (float): desired lower bound of xdata
        upper (float): desired upper bound of xdata

    Raises:
        ValueError: If length of xdata and ydata are not equal.
        ValueError: If upper bound is greater than the last value of xdata.
        ValueError: If xdata is not a monotonously rising sequence.

    Returns:
        tuple of np.ndarray: cropped xdata and ydata
    """
    xdata = np.asarray(xdata)
    ydata = np.asarray(ydata)

    if xdata.shape != ydata.shape:
        raise ValueError('Shape of xdata and ydata must be equal!')

    if xdata.ndim != 1:
        raise ValueError('Input data must be 1D arrays!')

    if xdata.size == 0:
        return xdata, ydata # Return empty arrays if input is empty

    if np.any(np.diff(xdata) <= 0):
        raise ValueError('xdata needs to be a monotonously rising sequence!')

    if lower < xdata[0]:
        lower = xdata[0] # Adjust lower bound to xdata[0] if it's smaller

    if upper > xdata[-1]:
        raise ValueError('Upper bound needs to be equal to or smaller than the last value of xdata!')

    # Use boolean indexing to find the desired range
    indices = (xdata >= lower) & (xdata <= upper)

    return xdata[indices], ydata[indices]