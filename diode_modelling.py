# -*- coding: utf-8 -*-
"""Classes and functions to calculate and use diode model parameters."""

import numpy as np
from scipy.optimize import curve_fit
import scipy.constants as const
from scipy.special import lambertw
from typing import Tuple
import logging

from diode_equations import ideal_diode_eq, get_ideal_diode_eq_log, ic_diode_ohmic, diode_saturation_current_standard
from diode_equations import NOMINAL_TEMPERATURE_K, SILICON_BANDGAP_VOLTAGE_EV, DEFAULT_FC, DEFAULT_CJO, DEFAULT_VJ, DEFAULT_M_GRADING, DEFAULT_TT, DEFAULT_IBV, DEFAULT_XTI
from diode_utils import crop_data_range_to_x
from config_loader import load_config

_config = load_config()



class DiodeModelIsotherm:
    def __init__(self, v_ca_a, i_c_a , c_ca_a , T, i_s=None, m=None, r_s=None, tt=None, cjo=None, vj=None, m_grading=None, fc=None, bv=None, ibv=None, eg=None, xti=None):
        self.T = T
        self.vca_lim_lower_ic = 0.7
        self.vca_lim_upper_ic = 0.75
        self.vca_lim_lower_cca = -100.0
        self.vca_lim_upper_cca = 1.0
        self.vca_lim_lower_r = -100.0
        self.vca_lim_upper_r = 1.0

        if i_s is None:  # Check if any parameter is None to trigger extraction
            self.params = diode_model_params_isotherm(v_ca_a, i_c_a, c_ca_a, T)
        else:
            self.params = {'T': T, 'I_S': i_s, 'm': m, 'R_S': r_s, 'TT': tt,
                           'CJO': cjo, 'VJ': vj, 'M_GRADING': m_grading, 'FC': fc,
                           'BV': bv, 'IBV': ibv, 'EG': eg, 'XTI': xti,
                           'vca_lim_lower_ic': self.vca_lim_lower_ic,
                           'vca_lim_upper_ic': self.vca_lim_upper_ic,
                           'vca_lim_lower_cca': self.vca_lim_lower_cca,
                           'vca_lim_upper_cca': self.vca_lim_upper_cca,
                           'vca_lim_lower_r': self.vca_lim_lower_r,
                           'vca_lim_upper_r': self.vca_lim_upper_r,
                           }
        self._set_attributes_from_params()
        self._update_labels()

    def _set_attributes_from_params(self):
        """Set instance attributes from the self.params dictionary."""
        self.i_s = self.params.get('I_S')
        self.m = self.params.get('m')
        self.r_s = self.params.get('R_S')
        self.tt = self.params.get('TT')
        self.cjo = self.params.get('CJO')
        self.vj = self.params.get('VJ')
        self.m_grading = self.params.get('M_GRADING')
        self.fc = self.params.get('FC')
        self.bv = self.params.get('BV')
        self.ibv = self.params.get('IBV')
        self.eg = self.params.get('EG')
        self.xti = self.params.get('XTI')
        self.vca_lim_lower_ic = self.params.get('vca_lim_lower_ic', self.vca_lim_lower_ic)
        self.vca_lim_upper_ic = self.params.get('vca_lim_upper_ic', self.vca_lim_upper_ic)
        self.vca_lim_lower_cca = self.params.get('vca_lim_lower_cca', self.vca_lim_lower_cca)
        self.vca_lim_upper_cca = self.params.get('vca_lim_upper_cca', self.vca_lim_upper_cca)
        self.vca_lim_lower_r = self.params.get('vca_lim_lower_r', self.vca_lim_lower_r)
        self.vca_lim_upper_r = self.params.get('vca_lim_upper_r', self.vca_lim_upper_r)

    def _update_labels(self):
        self.label_ideal_diode_model=(
            f'I_C_ideal = I_S * (exp (V_CA/(V_T*m)) -1):\n I_S = {self.i_s:.4g} A, m = {self.m:.4g}\n based on {self.vca_lim_lower_ic}V <= V_CA <= {self.vca_lim_upper_ic}V')
        self.label_diode_ohmic = (
            f'I_C_model (R_S = {self.r_s:.4g} $\Omega$)\n based on {self.vca_lim_lower_r}V <= V_CA <= {self.vca_lim_upper_r}V')
        self.label_cca_model = (
            f'C_CA_model = TT * I_C_model\n TT = {self.tt:.4g}s\n CJO = {self.cjo:.4g}F, VJ = {self.vj:.4g}V, M = {self.m_grading:.4g}, FC = {self.fc:.4g}\n based on {self.vca_lim_lower_cca}V <= V_CA <= {self.vca_lim_upper_cca}V')
        self.label_r = 'r_D = d(V_CA)/d(I_C)'

    def set_parameters(self, **kwargs):
        """Update model parameters from keyword arguments, used by the GUI for tuning."""
        for key, value in kwargs.items():
            # Map GUI keys to internal param keys if they differ
            param_key = key
            if key == "M_GRADING": # Example if GUI uses a different name
                param_key = "M_GRADING"
            elif key == "CJO":
                param_key = "CJO"
            elif key == "VJ":
                param_key = "VJ"
            elif key == "FC":
                param_key = "FC"
            if param_key in self.params:
                self.params[param_key] = value
            else:
                logging.warning(f"Attempted to set unknown parameter '{key}'")

        self._set_attributes_from_params()
        self._update_labels()

    def calc_ic_ideal_diode_a(self, v_ca_a):
        """Ideal diode current array as a function of a diode voltage."""
        def ideal_diode_eq_self(v_ca):
            return ideal_diode_eq(v_ca, self.i_s, self.m, self.T)
        ideal_diode_eq_self_vec = np.vectorize(ideal_diode_eq_self)
        i_c_ideal_diode_a = ideal_diode_eq_self_vec(v_ca_a)
        return i_c_ideal_diode_a

    def calc_ic_diode_ohmic_a(self, v_ca_a):
        """Current array from an ideal diode in series with a resistor."""
        def ic_diode_ohmic_self(v_ca):
            return ic_diode_ohmic(v_ca, self.i_s, self.m, self.T, self.r_s)
        ic_diode_ohmic_self_vec = np.vectorize(ic_diode_ohmic_self)
        i_c_r_a = ic_diode_ohmic_self_vec(v_ca_a)
        return i_c_r_a

    def calc_c_diode_a(self, i_c_r_a):
        """Capacitance array linearly dependent on diode current (model)."""
        def diode_capacitance_TT_eq_self(i_c_r):
            return diode_capacitance_TT_eq(i_c_r, self.tt)
        diode_capacitance_TT_eq_self_vec = np.vectorize(diode_capacitance_TT_eq_self)
        c_ca_model_a = diode_capacitance_TT_eq_self_vec(i_c_r_a)
        return c_ca_model_a

    def calc_total_capacitance_a(self, v_ca_a, i_c_a):
        """Calculate total diode capacitance (depletion + diffusion)."""
        # Diffusion capacitance
        c_diff = self.calc_c_diode_a(i_c_a)

        # Depletion capacitance
        def depletion_capacitance_eq_self(v_ca):
            return depletion_capacitance_eq(v_ca, self.cjo, self.vj, self.m_grading, self.fc)
        depletion_capacitance_eq_self_vec = np.vectorize(depletion_capacitance_eq_self)
        c_dep = depletion_capacitance_eq_self_vec(v_ca_a)

        return c_diff + c_dep


class DiodeModel(DiodeModelIsotherm):
    def __init__(self, v_ca_a, i_c_a , c_ca_a , T, T_i_s_a, i_s_temp_a , i_s=None, m=None, r_s=None, tt=None, cjo=None, vj=None, m_grading=None, fc=None, bv=None, ibv=None, eg=None, xti=None):
        super().__init__(v_ca_a, i_c_a , c_ca_a , T, i_s, m, r_s, tt, cjo, vj, m_grading, fc, bv, ibv, eg, xti)
        if T_i_s_a is not None and i_s_temp_a is not None:
            self._extract_temp_dependence(T_i_s_a, i_s_temp_a)

    def _extract_temp_dependence(self, T_a, i_s_a):
        """Extract EG and XTI from temperature-dependent saturation current data."""
        # Check if we have enough data points for fitting
        if len(T_a) < 2 or len(i_s_a) < 2:
            logging.warning(f"Insufficient data points for temperature dependence fitting (only {len(T_a)} points). Using default EG and XTI values.")
            # Find nominal temperature and current (closest to 300.15K)
            T_nom = NOMINAL_TEMPERATURE_K
            nom_index = (np.abs(T_a - T_nom)).argmin()
            i_s_nom = i_s_a[nom_index]
            # Update the base saturation current to the one at the nominal temperature
            self.i_s = i_s_nom
            self.params['I_S'] = self.i_s
            logging.info(f"Using single temperature data point: T = {T_a[nom_index]:.1f}K, I_S = {i_s_nom:.4g}A")
            return

        # Find nominal temperature and current (closest to 300.15K)
        T_nom = NOMINAL_TEMPERATURE_K
        nom_index = (np.abs(T_a - T_nom)).argmin()
        i_s_nom = i_s_a[nom_index]
        T_nom_actual = T_a[nom_index]

        # Wrapper for curve_fit
        def fit_func(T, eg, xti):
            return diode_saturation_current_standard(i_s_nom, T, T_nom_actual, eg, xti)

        try:
            p_opt, _ = curve_fit(fit_func, T_a, i_s_a, p0=[1.11, 3.0])
            self.eg, self.xti = p_opt
            self.params['EG'] = self.eg
            self.params['XTI'] = self.xti
            # Update the base saturation current to the one at the nominal temperature
            self.i_s = i_s_nom
            self.params['I_S'] = self.i_s
            logging.info(f"Extracted Temperature Parameters: EG = {self.eg:.4g} eV, XTI = {self.xti:.4g}")
        except (RuntimeError, TypeError) as e:
            logging.warning(f"Temperature dependence fitting failed: {e}. Using default EG and XTI.")
            # Find nominal temperature and current (closest to 300.15K)
            nom_index = (np.abs(T_a - T_nom)).argmin()
            i_s_nom = i_s_a[nom_index]
            # Update the base saturation current to the one at the nominal temperature
            self.i_s = i_s_nom
            self.params['I_S'] = self.i_s
            # Keep default values if fitting fails
            pass

    def calc_i_s_temp_a(self, T_lower = 250, T_upper = 450):
        T_a = np.linspace(T_lower, T_upper, num=100)
        i_s_a = np.zeros(len(T_a))
        T_nom = NOMINAL_TEMPERATURE_K # Assume nominal temperature for calculation
        for i in range(len(i_s_a)):
            i_s_a[i] = diode_saturation_current_standard(self.i_s, T_a[i], T_nom, self.eg, self.xti)
        return (T_a, i_s_a)


def ideal_diode_model(v_ca_a: np.ndarray, i_c_a: np.ndarray, vca_lim_lower: float = 0.65,
                          vca_lim_upper: float = 0.8, T: float = NOMINAL_TEMPERATURE_K) -> Tuple[float, float]:
    """Calculate a best fit model for the Shockley Diode equation

    Args:
        v_ca_a  (np.ndarray): Cathode-Anode voltage.
        i_c_a  (np.ndarray): Diode current.
        vca_lim_lower (float, optional):
            Lower limit in Volt of range the model is based on.
            Defaults to 0.65.
        vca_lim_upper (float, optional): [description].
            Upper limit in Volt of range the model is based on.
            Defaults to 0.8.

    Returns:
        i_s (float): Saturation current (model parameter).
        m (float): Ideality factor (model parameter)
    """
    v_ca_cropped, i_c_cropped = crop_data_range_to_x(v_ca_a , i_c_a ,
                                    vca_lim_lower, vca_lim_upper)

    log_vec = np.vectorize(np.log)
    diode_eq_T = get_ideal_diode_eq_log(T)
    p_opt, pcov = curve_fit(diode_eq_T, v_ca_cropped, log_vec(i_c_cropped))
    i_s = np.exp(p_opt[0])      # Result of ideal_diode_eq_log is log(i_s)
    m = p_opt[1]

    return (i_s, m)


def diode_capacitance_TT_eq(i_c: np.ndarray, tt: float) -> np.ndarray:
    """Diode capacitance as function of diode current and transit time

    Diffusion capacitance is linearly dependent on diode current.
    Args:
        i_c (float: Diode current.
        tt (float): Transit time.

    Returns:
        np.ndarray: Diode capacitance.
    """
    c_ca = tt * i_c
    return c_ca

def depletion_capacitance_eq(v_ca: np.ndarray, cjo: float, vj: float, m_grading: float, fc: float) -> np.ndarray:
    """HSpice Level 3 Depletion Capacitance equation.

    Args:
        v_ca (float): Cathode-Anode voltage [V].
        cjo (float): Zero-bias junction capacitance [F].
        vj (float): Junction potential [V].
        m_grading (float): Grading coefficient.
        fc (float): Forward-bias depletion capacitance coefficient.

    Returns:
        np.ndarray: Depletion capacitance [F].
    """
    # Add a small epsilon to the denominator to prevent division by zero or sqrt of negative number
    epsilon = 1e-9
    denominator = (1 - v_ca / vj) + epsilon

    c_dep = np.where(v_ca < fc * vj,
                     cjo / (denominator**m_grading),
                     cjo / ((1 - fc)**m_grading) * (1 + m_grading * (v_ca - fc * vj) / (vj * (1 - fc))))
    return c_dep

def total_capacitance_eq(v_ca: np.ndarray, cjo: float, vj: float, m_grading: float, fc: float, tt: float, i_c: np.ndarray) -> np.ndarray:
    """Total diode capacitance (depletion + diffusion).

    Args:
        v_ca (float): Cathode-Anode voltage [V].
        cjo (float): Zero-bias junction capacitance [F].
        vj (float): Junction potential [V].
        m_grading (float): Grading coefficient.
        fc (float): Forward-bias depletion capacitance coefficient.
        tt (float): Transit time.
        i_c (float): Diode current.

    Returns:
        np.ndarray: Total capacitance [F].
    """
    c_diff = diode_capacitance_TT_eq(i_c, tt)
    c_dep = depletion_capacitance_eq(v_ca, cjo, vj, m_grading, fc)
    return c_diff + c_dep


def diode_capacitance_model(v_ca_a: np.ndarray, i_c_a: np.ndarray, c_ca_a: np.ndarray, vca_lim_lower: float = 0.65,
                            vca_lim_upper: float = 0.8, fc: float = DEFAULT_FC) -> Tuple[float, float, float, float]:
    """Calculate a best fit model for the total diode capacitance (HSpice Level 3).

    Args:
        v_ca_a  (np.ndarray): Cathode-Anode voltage.
        i_c_a  (np.ndarray): Diode current.
        c_ca_a  (np.ndarray): Diode capacitance.
        vca_lim_lower (float, optional):
            Lower limit in Volt of range the model is based on.
            Defaults to 0.65.
        vca_lim_upper (float, optional): [description].
            Upper limit in Volt of range the model is based on.
            Defaults to 0.8.
        fc (float, optional): Forward-bias depletion capacitance coefficient. Defaults to 0.5.

    Returns:
        tuple: (cjo, vj, m_grading, tt)
            cjo (float): Zero-bias junction capacitance.
            vj (float): Junction potential.
            m_grading (float): Grading coefficient.
            tt (float): Transit time.
    """
    v_ca_cropped, c_ca_cropped = crop_data_range_to_x(v_ca_a , c_ca_a ,
                                            vca_lim_lower, vca_lim_upper)
    v_ca_cropped_ic, i_c_cropped = crop_data_range_to_x(v_ca_a , i_c_a ,
                                            vca_lim_lower, vca_lim_upper)

    # Ensure enough data points for fitting
    if len(v_ca_cropped) < 4 or len(c_ca_cropped) < 4 or len(i_c_cropped) < 4: # Need at least 4 parameters to fit
        print("Warning: Not enough data points for capacitance model fitting. Returning default parameters.")
        return DEFAULT_CJO, DEFAULT_VJ, DEFAULT_M_GRADING, DEFAULT_TT # Default CJO, VJ, M_GRADING, TT

    # Ensure no NaN or Inf values
    if np.any(np.isnan(v_ca_cropped)) or np.any(np.isinf(v_ca_cropped)) or         np.any(np.isnan(c_ca_cropped)) or np.any(np.isinf(c_ca_cropped)) or         np.any(np.isnan(i_c_cropped)) or np.any(np.isinf(i_c_cropped)):        logging.warning("Invalid data (NaN/Inf) for capacitance model fitting. Returning default parameters.")
    return DEFAULT_CJO, DEFAULT_VJ, DEFAULT_M_GRADING, DEFAULT_TT # Default CJO, VJ, M_GRADING, TT

    # Define the function to be fitted, passing i_c_cropped as an additional argument
    # In older SciPy versions, 'args' is not passed to the function being fitted.
    # Instead, we use a closure to capture i_c_cropped.
    def func_to_fit_capacitance(v_ca, cjo, vj, m_grading, tt):
        return total_capacitance_eq(v_ca, cjo, vj, m_grading, fc, tt, i_c_cropped)

    try:
        p_opt, pcov = curve_fit(
            func_to_fit_capacitance,
            v_ca_cropped,
            c_ca_cropped,
            p0=[DEFAULT_CJO, DEFAULT_VJ, DEFAULT_M_GRADING, DEFAULT_TT],  # CJO, VJ, M_GRADING, TT
            bounds=([1e-15, 0.1, 0.1, 1e-15], [1e-9, 1.0, 1.0, 1e-6]),
            maxfev=5000,
            # args=(i_c_cropped,) # Removed this as it\'s not supported in older SciPy versions for this usage
        )
        cjo, vj, m_grading, tt = p_opt
        return cjo, vj, m_grading, tt
    except (RuntimeError, ValueError) as e:
        logging.warning(f"curve_fit failed in diode_capacitance_model: {e}. Returning default capacitance parameters.")
        return DEFAULT_CJO, DEFAULT_VJ, DEFAULT_M_GRADING, DEFAULT_TT # Default CJO, VJ, M_GRADING, TT


def _extract_breakdown_parameters(v_ca_a: np.ndarray, i_c_a: np.ndarray, ibv_threshold: float = DEFAULT_IBV) -> Tuple[float, float]:
    """Extracts breakdown voltage (BV) and current (IBV) from reverse characteristics.

    Args:
        v_ca_a (np.ndarray): Cathode-Anode voltage array.
        i_c_a (np.ndarray): Diode current array.
        ibv_threshold (float): The current threshold to define breakdown.

    Returns:
        Tuple[float, float]: A tuple containing (bv, ibv).
                             bv will be np.inf if no breakdown is detected.
    """
    reverse_mask = v_ca_a < 0
    v_rev = v_ca_a[reverse_mask]
    i_rev = np.abs(i_c_a[reverse_mask])

    breakdown_indices = np.where(i_rev >= ibv_threshold)[0]
    if breakdown_indices.size > 0:
        first_breakdown_index = breakdown_indices[0]
        bv = -v_rev[first_breakdown_index]
        ibv = i_rev[first_breakdown_index]
        logging.info(f'Breakdown Voltage BV = {bv:.4g} V at IBV = {ibv:.4g} A')
    else:
        bv = np.inf  # No breakdown found
        ibv = ibv_threshold
        logging.info('No reverse breakdown detected.')
    return bv, ibv

def _extract_series_resistance(v_ca_a: np.ndarray, i_c_a: np.ndarray, vca_lim_lower_r: float, vca_lim_upper_r: float) -> float:
    """Extracts the series resistance (R_S) from I-V characteristics.

    Args:
        v_ca_a (np.ndarray): Cathode-Anode voltage array.
        i_c_a (np.ndarray): Diode current array.
        vca_lim_lower_r (float): Lower voltage limit for resistance calculation.
        vca_lim_upper_r (float): Upper voltage limit for resistance calculation.

    Returns:
        float: The calculated series resistance (r_ohm_simple).
    """
    vca_lim_lower_r_i_arr = np.where(np.isclose(v_ca_a, vca_lim_lower_r, rtol=1e-2))[0]
    if vca_lim_lower_r_i_arr.size > 0:
        vca_lim_lower_r_i = vca_lim_lower_r_i_arr[0]
    else:
        vca_lim_lower_r_i = (np.abs(v_ca_a - vca_lim_lower_r)).argmin()

    vca_lim_upper_r_i_arr = np.where(np.isclose(v_ca_a, vca_lim_upper_r, rtol=1e-2))[0]
    if vca_lim_upper_r_i_arr.size > 0:
        vca_lim_upper_r_i = vca_lim_upper_r_i_arr[0]
    else:
        vca_lim_upper_r_i = (np.abs(v_ca_a - vca_lim_upper_r)).argmin()
    
    # Ensure indices are valid and within bounds
    if vca_lim_lower_r_i >= len(v_ca_a) or vca_lim_upper_r_i >= len(v_ca_a) or \
       vca_lim_lower_r_i < 0 or vca_lim_upper_r_i < 0 or \
       vca_lim_lower_r_i == vca_lim_upper_r_i:
        logging.warning("Invalid indices for series resistance calculation. Returning default 0.0.")
        return 0.0

    delta_v = v_ca_a[vca_lim_upper_r_i] - v_ca_a[vca_lim_lower_r_i]
    delta_i = i_c_a[vca_lim_upper_r_i] - i_c_a[vca_lim_lower_r_i]

    if delta_i == 0:
        logging.warning("Change in current is zero for series resistance calculation. Returning default 0.0.")
        return 0.0

    r_ohm_simple = delta_v / delta_i
    logging.info(f'R_S_simple = {r_ohm_simple}')
    return r_ohm_simple

def diode_model_params_isotherm(v_ca_a, i_c_a , c_ca_a , T):
    """Extract diode model parameters at a fixed temperature"""
    vca_lim_lower_ic = 0.65
    vca_lim_upper_ic = 0.75
    i_s, m = ideal_diode_model(v_ca_a, i_c_a , vca_lim_lower_ic, vca_lim_upper_ic, T)

    print(f'Model parameters for T = {T:.0f} K: I_S = {i_s} A, m = {m}')

    vca_lim_lower_r = -100.0
    vca_lim_upper_r = 1.0
    r_ohm_simple = _extract_series_resistance(v_ca_a, i_c_a, vca_lim_lower_r, vca_lim_upper_r)

    bv, ibv = _extract_breakdown_parameters(v_ca_a, i_c_a, DEFAULT_IBV)

    vca_lim_lower_cca = -100.0
    vca_lim_upper_cca = 1.0
    # Now diode_capacitance_model extracts CJO, VJ, M_GRADING, TT
    cjo, vj, m_grading, tt = diode_capacitance_model(v_ca_a, i_c_a , c_ca_a , vca_lim_lower_cca, vca_lim_upper_cca)

    logging.info(f'Transit time for T = {T:.0f} K: TT = {tt:.4g} s.')
    logging.info(f'CJO = {cjo:.4g}F, VJ = {vj:.4g}V, M = {m_grading:.4g}')

    if np.isinf(bv):
        bv = None # Convert np.inf to None for JSON compatibility

    model_params = {'T': T, 'I_S': i_s, 'm': m, 'R_S': r_ohm_simple,
                    'TT': tt, 'CJO': cjo, 'VJ': vj, 'M_GRADING': m_grading, 'FC': DEFAULT_FC,
                    'BV': bv, 'IBV': ibv, 'EG': SILICON_BANDGAP_VOLTAGE_EV, 'XTI': DEFAULT_XTI,
                    'vca_lim_lower_ic': vca_lim_lower_ic,
                    'vca_lim_upper_ic': vca_lim_upper_ic,
                    'vca_lim_lower_cca': vca_lim_lower_cca,
                    'vca_lim_upper_cca': vca_lim_upper_cca,
                    'vca_lim_lower_r': vca_lim_lower_r,
                    'vca_lim_upper_r': vca_lim_upper_r,
                    }
    return model_params