# -*- coding: utf-8 -*-
"""Classes and functions to calculate and use diode model parameters."""

import numpy as np
from scipy.optimize import curve_fit
import scipy.constants as const
from scipy.special import lambertw
from typing import Tuple
import logging

from diode_equations import ideal_diode_eq, get_ideal_diode_eq_log, ic_diode_ohmic, diode_saturation_current_standard
from diode_equations import NOMINAL_TEMPERATURE_K, SILICON_BANDGAP_VOLTAGE_EV, DEFAULT_FC, DEFAULT_CJO, DEFAULT_VJ, DEFAULT_M, DEFAULT_M_GRADING, DEFAULT_TT, DEFAULT_IBV, DEFAULT_XTI, DEFAULT_EG, DEFAULT_IS, DEFAULT_N, DEFAULT_RS, DEFAULT_BV
from diode_utils import crop_data_range_to_x
from config_loader import load_config

_config = load_config()



class DiodeModelIsotherm:
    """HSPICE Level=3 compatible diode model for isothermal conditions"""
    def __init__(self, v_ca_a, i_c_a , c_ca_a , T, IS=None, N=None, RS=None, TT=None, CJO=None, VJ=None, M=None, FC=None, BV=None, IBV=None, EG=None, XTI=None, JTUN=None, JTUNSW=None, NTUN=None):
        self.T = T
        self.vca_lim_lower_ic = 0.7
        self.vca_lim_upper_ic = 0.75
        self.vca_lim_lower_cca = -100.0
        self.vca_lim_upper_cca = 1.0
        self.vca_lim_lower_r = -100.0
        self.vca_lim_upper_r = 1.0

        if IS is None:  # Check if any parameter is None to trigger extraction
            self.params = diode_model_params_isotherm(v_ca_a, i_c_a, c_ca_a, T)
        else:
            self.params = {'T': T, 'IS': IS, 'N': N, 'RS': RS, 'TT': TT,
                           'CJO': CJO, 'VJ': VJ, 'M': M, 'FC': FC,
                           'BV': BV, 'IBV': IBV, 'EG': EG, 'XTI': XTI,
                           'JTUN': JTUN, 'JTUNSW': JTUNSW, 'NTUN': NTUN,
                           'vca_lim_lower_ic': self.vca_lim_lower_ic,
                           'vca_lim_upper_ic': self.vca_lim_upper_ic,
                           'vca_lim_lower_cca': self.vca_lim_lower_cca,
                           'vca_lim_upper_cca': self.vca_lim_upper_cca,
                           'vca_lim_lower_r': self.vca_lim_lower_r,
                           'vca_lim_upper_r': self.vca_lim_upper_r,
                           }
        self._set_attributes_from_params()
        self._update_labels()

    def _set_attributes_from_params(self):
        """Set instance attributes from the self.params dictionary using HSPICE Level=3 parameter names."""
        # HSPICE Level=3 standard parameter names
        self.IS = self.params.get('IS')
        self.N = self.params.get('N')
        self.RS = self.params.get('RS')
        self.TT = self.params.get('TT')
        self.CJO = self.params.get('CJO')
        self.VJ = self.params.get('VJ')
        self.M = self.params.get('M')
        self.FC = self.params.get('FC')
        self.BV = self.params.get('BV')
        self.IBV = self.params.get('IBV')
        self.EG = self.params.get('EG')
        self.XTI = self.params.get('XTI')
        # HSPICE Level=3 tunneling parameters
        self.JTUN = self.params.get('JTUN')
        self.JTUNSW = self.params.get('JTUNSW')
        self.NTUN = self.params.get('NTUN')

        # Legacy aliases for backward compatibility
        self.i_s = self.IS
        self.m = self.N
        self.r_s = self.RS
        self.tt = self.TT
        self.cjo = self.CJO
        self.vj = self.VJ
        self.m_grading = self.M
        self.fc = self.FC
        self.bv = self.BV
        self.ibv = self.IBV
        self.eg = self.EG
        self.xti = self.XTI
        # Tunneling parameter aliases
        self.jtun = self.JTUN
        self.jtunsw = self.JTUNSW
        self.ntun = self.NTUN
        self.vca_lim_lower_ic = self.params.get('vca_lim_lower_ic', self.vca_lim_lower_ic)
        self.vca_lim_upper_ic = self.params.get('vca_lim_upper_ic', self.vca_lim_upper_ic)
        self.vca_lim_lower_cca = self.params.get('vca_lim_lower_cca', self.vca_lim_lower_cca)
        self.vca_lim_upper_cca = self.params.get('vca_lim_upper_cca', self.vca_lim_upper_cca)
        self.vca_lim_lower_r = self.params.get('vca_lim_lower_r', self.vca_lim_lower_r)
        self.vca_lim_upper_r = self.params.get('vca_lim_upper_r', self.vca_lim_upper_r)

    def _update_labels(self):
        self.label_ideal_diode_model=(
            f'I_C_ideal = I_S * (exp (V_CA/(V_T*m)) -1):\n I_S = {self.i_s:.4g} A, m = {self.m:.4g}\n based on {self.vca_lim_lower_ic}V <= V_CA <= {self.vca_lim_upper_ic}V')
        self.label_diode_ohmic = (
            f'I_C_model (R_S = {self.r_s:.4g} $\Omega$)\n based on {self.vca_lim_lower_r}V <= V_CA <= {self.vca_lim_upper_r}V')
        self.label_cca_model = (
            f'C_CA_model = TT * I_C_model\n TT = {self.tt:.4g}s\n CJO = {self.cjo:.4g}F, VJ = {self.vj:.4g}V, M = {self.m_grading:.4g}, FC = {self.fc:.4g}\n based on {self.vca_lim_lower_cca}V <= V_CA <= {self.vca_lim_upper_cca}V')
        self.label_r = 'r_D = d(V_CA)/d(I_C)'

    def set_parameters(self, **kwargs):
        """Update model parameters from keyword arguments, used by the GUI for tuning (HSPICE Level=3 compatible)."""
        for key, value in kwargs.items():
            # Skip None values (e.g., BV when no breakdown detected)
            if value is None:
                continue

            # Map GUI keys to internal param keys if they differ
            param_key = key
            if key == "M_GRADING": # Legacy compatibility
                param_key = "M"
            elif key == "m": # Legacy compatibility
                param_key = "N"
            elif key == "i_s": # Legacy compatibility
                param_key = "IS"
            elif key == "r_s": # Legacy compatibility
                param_key = "RS"
            elif key == "tt": # Legacy compatibility
                param_key = "TT"
            elif key == "cjo": # Legacy compatibility
                param_key = "CJO"
            elif key == "vj": # Legacy compatibility
                param_key = "VJ"
            elif key == "m_grading": # Legacy compatibility
                param_key = "M"
            elif key == "fc": # Legacy compatibility
                param_key = "FC"
            elif key == "bv": # Legacy compatibility
                param_key = "BV"
            elif key == "ibv": # Legacy compatibility
                param_key = "IBV"
            elif key == "eg": # Legacy compatibility
                param_key = "EG"
            elif key == "xti": # Legacy compatibility
                param_key = "XTI"
            elif key == "jtun": # Legacy compatibility
                param_key = "JTUN"
            elif key == "jtunsw": # Legacy compatibility
                param_key = "JTUNSW"
            elif key == "ntun": # Legacy compatibility
                param_key = "NTUN"

            # Always allow setting parameters, add to params if not present
            self.params[param_key] = value

        self._set_attributes_from_params()
        self._update_labels()

    def calc_ic_ideal_diode_a(self, v_ca_a):
        """Ideal diode current array as a function of a diode voltage."""
        def ideal_diode_eq_self(v_ca):
            return ideal_diode_eq(v_ca, self.i_s, self.m, self.T)
        ideal_diode_eq_self_vec = np.vectorize(ideal_diode_eq_self)
        i_c_ideal_diode_a = ideal_diode_eq_self_vec(v_ca_a)
        return i_c_ideal_diode_a

    def calc_ic_diode_ohmic_a(self, v_ca_a):
        """Current array from complete SPICE Level=3 diode model."""
        from diode_equations import ic_diode_spice_level3

        # Use SPICE Level=3 model with all parameters
        i_c_r_a = ic_diode_spice_level3(
            v_ca_a,
            IS=self.IS or self.i_s,  # Use uppercase or lowercase
            N=self.N or self.m,
            T=self.T,
            RS=self.RS or self.r_s,
            BV=getattr(self, 'BV', None) or getattr(self, 'bv', None),
            IBV=getattr(self, 'IBV', 1e-3) or getattr(self, 'ibv', 1e-3),
            JTUN=getattr(self, 'JTUN', 0.0) or getattr(self, 'jtun', 0.0),
            JTUNSW=getattr(self, 'JTUNSW', 0.0) or getattr(self, 'jtunsw', 0.0),
            NTUN=getattr(self, 'NTUN', 30.0) or getattr(self, 'ntun', 30.0),
            GMIN=1e-12  # Standard SPICE convergence conductance
        )
        return i_c_r_a

    def calc_c_diode_spice_a(self, v_ca_a):
        """Capacitance array from SPICE Level=3 diode model."""
        from diode_equations import diode_junction_capacitance_spice

        # Use SPICE Level=3 capacitance model
        c_ca_a = diode_junction_capacitance_spice(
            v_ca_a,
            CJ=getattr(self, 'CJO', 1e-12) or getattr(self, 'cjo', 1e-12),
            PB=getattr(self, 'VJ', 0.7) or getattr(self, 'vj', 0.7),
            MJ=getattr(self, 'M', 0.5) or getattr(self, 'm_grading', 0.5),
            FC=getattr(self, 'FC', 0.5) or getattr(self, 'fc', 0.5),
            CJP=0.0,  # Sidewall capacitance (not typically extracted from IV data)
            PHP=0.75,
            MJSW=0.33
        )
        return c_ca_a

    def calc_c_diode_a(self, i_c_r_a):
        """Capacitance array linearly dependent on diode current (model)."""
        def diode_capacitance_TT_eq_self(i_c_r):
            return diode_capacitance_TT_eq(i_c_r, self.tt)
        diode_capacitance_TT_eq_self_vec = np.vectorize(diode_capacitance_TT_eq_self)
        c_ca_model_a = diode_capacitance_TT_eq_self_vec(i_c_r_a)
        return c_ca_model_a

    def calc_total_capacitance_a(self, v_ca_a, i_c_a):
        """Calculate total diode capacitance (depletion + diffusion)."""
        # Diffusion capacitance
        c_diff = self.calc_c_diode_a(i_c_a)

        # Depletion capacitance
        def depletion_capacitance_eq_self(v_ca):
            return depletion_capacitance_eq(v_ca, self.cjo, self.vj, self.m_grading, self.fc)
        depletion_capacitance_eq_self_vec = np.vectorize(depletion_capacitance_eq_self)
        c_dep = depletion_capacitance_eq_self_vec(v_ca_a)

        return c_diff + c_dep


class DiodeModel(DiodeModelIsotherm):
    """HSPICE Level=3 compatible diode model with temperature dependence"""
    def __init__(self, v_ca_a, i_c_a , c_ca_a , T, T_i_s_a, i_s_temp_a , IS=None, N=None, RS=None, TT=None, CJO=None, VJ=None, M=None, FC=None, BV=None, IBV=None, EG=None, XTI=None, JTUN=None, JTUNSW=None, NTUN=None):
        super().__init__(v_ca_a, i_c_a , c_ca_a , T, IS, N, RS, TT, CJO, VJ, M, FC, BV, IBV, EG, XTI, JTUN, JTUNSW, NTUN)
        if T_i_s_a is not None and i_s_temp_a is not None:
            self._extract_temp_dependence(T_i_s_a, i_s_temp_a)

    def _extract_temp_dependence(self, T_a, IS_a):
        """Extract EG and XTI from temperature-dependent saturation current data (HSPICE Level=3 compatible)."""
        # Check if we have enough data points for fitting
        if len(T_a) < 2 or len(IS_a) < 2:
            logging.warning(f"Insufficient data points for temperature dependence fitting (only {len(T_a)} points). Using default EG and XTI values.")
            # Find nominal temperature and current (closest to 300.15K)
            T_nom = NOMINAL_TEMPERATURE_K
            nom_index = (np.abs(T_a - T_nom)).argmin()
            IS_nom = IS_a[nom_index]
            # Update the base saturation current to the one at the nominal temperature
            self.IS = IS_nom
            self.i_s = IS_nom  # Legacy alias
            self.params['IS'] = self.IS
            logging.info(f"Using single temperature data point: T = {T_a[nom_index]:.1f}K, IS = {IS_nom:.4g}A")
            return

        # Find nominal temperature and current (closest to 300.15K)
        T_nom = NOMINAL_TEMPERATURE_K
        nom_index = (np.abs(T_a - T_nom)).argmin()
        IS_nom = IS_a[nom_index]
        T_nom_actual = T_a[nom_index]

        # Wrapper for curve_fit
        def fit_func(T, EG, XTI):
            return diode_saturation_current_standard(IS_nom, T, T_nom_actual, EG, XTI)

        try:
            p_opt, _ = curve_fit(fit_func, T_a, IS_a, p0=[1.11, 3.0])
            self.EG, self.XTI = p_opt
            self.eg, self.xti = self.EG, self.XTI  # Legacy aliases
            self.params['EG'] = self.EG
            self.params['XTI'] = self.XTI
            # Update the base saturation current to the one at the nominal temperature
            self.IS = IS_nom
            self.i_s = IS_nom  # Legacy alias
            self.params['IS'] = self.IS
            logging.info(f"Extracted Temperature Parameters: EG = {self.EG:.4g} eV, XTI = {self.XTI:.4g}")
        except (RuntimeError, TypeError) as e:
            logging.warning(f"Temperature dependence fitting failed: {e}. Using default EG and XTI.")
            # Find nominal temperature and current (closest to 300.15K)
            nom_index = (np.abs(T_a - T_nom)).argmin()
            IS_nom = IS_a[nom_index]
            # Update the base saturation current to the one at the nominal temperature
            self.IS = IS_nom
            self.i_s = IS_nom  # Legacy alias
            self.params['IS'] = self.IS
            # Keep default values if fitting fails
            pass

    def calc_IS_temp_a(self, T_lower = 250, T_upper = 450):
        """Calculate IS vs temperature array using HSPICE Level=3 parameters"""
        T_a = np.linspace(T_lower, T_upper, num=100)
        IS_a = np.zeros(len(T_a))
        T_nom = NOMINAL_TEMPERATURE_K # Assume nominal temperature for calculation
        for i in range(len(IS_a)):
            IS_a[i] = diode_saturation_current_standard(self.IS, T_a[i], T_nom, self.EG, self.XTI)
        return (T_a, IS_a)

    # Legacy method name for backward compatibility
    def calc_i_s_temp_a(self, T_lower = 250, T_upper = 450):
        """Legacy method - use calc_IS_temp_a instead"""
        return self.calc_IS_temp_a(T_lower, T_upper)


def ideal_diode_model(v_ca_a: np.ndarray, i_c_a: np.ndarray, vca_lim_lower: float = 0.65,
                          vca_lim_upper: float = 0.8, T: float = NOMINAL_TEMPERATURE_K) -> Tuple[float, float]:
    """Calculate a best fit model for the Shockley Diode equation (HSPICE Level=3 compatible)

    Args:
        v_ca_a  (np.ndarray): Cathode-Anode voltage.
        i_c_a  (np.ndarray): Diode current.
        vca_lim_lower (float, optional):
            Lower limit in Volt of range the model is based on.
            Defaults to 0.65.
        vca_lim_upper (float, optional): [description].
            Upper limit in Volt of range the model is based on.
            Defaults to 0.8.

    Returns:
        IS (float): Saturation current (HSPICE Level=3 parameter).
        N (float): Ideality factor (HSPICE Level=3 parameter)
    """
    v_ca_cropped, i_c_cropped = crop_data_range_to_x(v_ca_a , i_c_a ,
                                    vca_lim_lower, vca_lim_upper)

    log_vec = np.vectorize(np.log)
    diode_eq_T = get_ideal_diode_eq_log(T)
    p_opt, _ = curve_fit(diode_eq_T, v_ca_cropped, log_vec(i_c_cropped))
    IS = np.exp(p_opt[0])      # Result of ideal_diode_eq_log is log(IS)
    N = p_opt[1]

    return (IS, N)


def diode_capacitance_TT_eq(i_c: np.ndarray, TT: float) -> np.ndarray:
    """Diode diffusion capacitance as function of diode current and transit time - HSPICE Level=3 compatible

    Diffusion capacitance is linearly dependent on diode current.
    Args:
        i_c (float): Diode current.
        TT (float): Transit time.

    Returns:
        np.ndarray: Diode diffusion capacitance.
    """
    c_ca = TT * i_c
    return c_ca

def depletion_capacitance_eq(v_ca: np.ndarray, CJO: float, VJ: float, M: float, FC: float) -> np.ndarray:
    """HSPICE Level=3 Depletion Capacitance equation.

    Args:
        v_ca (float): Cathode-Anode voltage [V].
        CJO (float): Zero-bias junction capacitance [F].
        VJ (float): Junction potential [V].
        M (float): Grading coefficient.
        FC (float): Forward-bias depletion capacitance coefficient.

    Returns:
        np.ndarray: Depletion capacitance [F].
    """
    # Add a small epsilon to the denominator to prevent division by zero or sqrt of negative number
    epsilon = 1e-9
    denominator = (1 - v_ca / VJ) + epsilon

    c_dep = np.where(v_ca < FC * VJ,
                     CJO / (denominator**M),
                     CJO / ((1 - FC)**M) * (1 + M * (v_ca - FC * VJ) / (VJ * (1 - FC))))
    return c_dep

def total_capacitance_eq(v_ca: np.ndarray, CJO: float, VJ: float, M: float, FC: float, TT: float, i_c: np.ndarray) -> np.ndarray:
    """Total diode capacitance (depletion + diffusion) - HSPICE Level=3 compatible.

    Args:
        v_ca (float): Cathode-Anode voltage [V].
        CJO (float): Zero-bias junction capacitance [F].
        VJ (float): Junction potential [V].
        M (float): Grading coefficient.
        FC (float): Forward-bias depletion capacitance coefficient.
        TT (float): Transit time.
        i_c (float): Diode current.

    Returns:
        np.ndarray: Total capacitance [F].
    """
    c_diff = diode_capacitance_TT_eq(i_c, TT)
    c_dep = depletion_capacitance_eq(v_ca, CJO, VJ, M, FC)
    return c_diff + c_dep


def diode_capacitance_model(v_ca_a: np.ndarray, i_c_a: np.ndarray, c_ca_a: np.ndarray, vca_lim_lower: float = 0.65,
                            vca_lim_upper: float = 0.8, fc: float = DEFAULT_FC) -> Tuple[float, float, float, float]:
    """Calculate a best fit model for the total diode capacitance (HSpice Level 3).

    Args:
        v_ca_a  (np.ndarray): Cathode-Anode voltage.
        i_c_a  (np.ndarray): Diode current.
        c_ca_a  (np.ndarray): Diode capacitance.
        vca_lim_lower (float, optional):
            Lower limit in Volt of range the model is based on.
            Defaults to 0.65.
        vca_lim_upper (float, optional): [description].
            Upper limit in Volt of range the model is based on.
            Defaults to 0.8.
        fc (float, optional): Forward-bias depletion capacitance coefficient. Defaults to 0.5.

    Returns:
        tuple: (cjo, vj, m_grading, tt)
            cjo (float): Zero-bias junction capacitance.
            vj (float): Junction potential.
            m_grading (float): Grading coefficient.
            tt (float): Transit time.
    """
    v_ca_cropped, c_ca_cropped = crop_data_range_to_x(v_ca_a , c_ca_a ,
                                            vca_lim_lower, vca_lim_upper)
    v_ca_cropped_ic, i_c_cropped = crop_data_range_to_x(v_ca_a , i_c_a ,
                                            vca_lim_lower, vca_lim_upper)

    # Ensure enough data points for fitting
    if len(v_ca_cropped) < 4 or len(c_ca_cropped) < 4 or len(i_c_cropped) < 4: # Need at least 4 parameters to fit
        print("Warning: Not enough data points for capacitance model fitting. Returning default parameters.")
        return DEFAULT_CJO, DEFAULT_VJ, DEFAULT_M_GRADING, DEFAULT_TT # Default CJO, VJ, M_GRADING, TT

    # Ensure no NaN or Inf values
    if np.any(np.isnan(v_ca_cropped)) or np.any(np.isinf(v_ca_cropped)) or \
        np.any(np.isnan(c_ca_cropped)) or np.any(np.isinf(c_ca_cropped)) or \
        np.any(np.isnan(i_c_cropped)) or np.any(np.isinf(i_c_cropped)):
        logging.warning("Invalid data (NaN/Inf) for capacitance model fitting. Returning default parameters.")
        return DEFAULT_CJO, DEFAULT_VJ, DEFAULT_M, DEFAULT_TT # Default CJO, VJ, M, TT

    # Define the function to be fitted, passing i_c_cropped as an additional argument
    # In older SciPy versions, 'args' is not passed to the function being fitted.
    # Instead, we use a closure to capture i_c_cropped.
    def func_to_fit_capacitance(v_ca, CJO, VJ, M, TT):
        return total_capacitance_eq(v_ca, CJO, VJ, M, fc, TT, i_c_cropped)

    try:
        p_opt, _ = curve_fit(
            func_to_fit_capacitance,
            v_ca_cropped,
            c_ca_cropped,
            p0=[DEFAULT_CJO, DEFAULT_VJ, DEFAULT_M, DEFAULT_TT],  # CJO, VJ, M, TT
            bounds=([1e-15, 0.1, 0.1, 1e-15], [1e-9, 1.0, 1.0, 1e-6]),
            maxfev=5000,
            # args=(i_c_cropped,) # Removed this as it's not supported in older SciPy versions for this usage
        )
        CJO, VJ, M, TT = p_opt
        return CJO, VJ, M, TT
    except (RuntimeError, ValueError) as e:
        logging.warning(f"curve_fit failed in diode_capacitance_model: {e}. Returning default capacitance parameters.")
        return DEFAULT_CJO, DEFAULT_VJ, DEFAULT_M, DEFAULT_TT # Default CJO, VJ, M, TT


def _extract_breakdown_parameters(v_ca_a: np.ndarray, i_c_a: np.ndarray, ibv_threshold: float = DEFAULT_IBV) -> Tuple[float, float]:
    """Extracts breakdown voltage (BV) and current (IBV) from reverse characteristics.

    Args:
        v_ca_a (np.ndarray): Cathode-Anode voltage array.
        i_c_a (np.ndarray): Diode current array.
        ibv_threshold (float): The current threshold to define breakdown.

    Returns:
        Tuple[float, float]: A tuple containing (bv, ibv).
                             bv will be np.inf if no breakdown is detected.
    """
    reverse_mask = v_ca_a < 0
    v_rev = v_ca_a[reverse_mask]
    i_rev = np.abs(i_c_a[reverse_mask])

    breakdown_indices = np.where(i_rev >= ibv_threshold)[0]
    if breakdown_indices.size > 0:
        first_breakdown_index = breakdown_indices[0]
        bv = -v_rev[first_breakdown_index]
        ibv = i_rev[first_breakdown_index]
        logging.info(f'Breakdown Voltage BV = {bv:.4g} V at IBV = {ibv:.4g} A')
    else:
        bv = np.inf  # No breakdown found
        ibv = ibv_threshold
        logging.info('No reverse breakdown detected.')
    return bv, ibv

def _extract_series_resistance(v_ca_a: np.ndarray, i_c_a: np.ndarray, vca_lim_lower_r: float, vca_lim_upper_r: float) -> float:
    """Extracts the series resistance (R_S) from I-V characteristics.

    Args:
        v_ca_a (np.ndarray): Cathode-Anode voltage array.
        i_c_a (np.ndarray): Diode current array.
        vca_lim_lower_r (float): Lower voltage limit for resistance calculation.
        vca_lim_upper_r (float): Upper voltage limit for resistance calculation.

    Returns:
        float: The calculated series resistance (r_ohm_simple).
    """
    vca_lim_lower_r_i_arr = np.where(np.isclose(v_ca_a, vca_lim_lower_r, rtol=1e-2))[0]
    if vca_lim_lower_r_i_arr.size > 0:
        vca_lim_lower_r_i = vca_lim_lower_r_i_arr[0]
    else:
        vca_lim_lower_r_i = (np.abs(v_ca_a - vca_lim_lower_r)).argmin()

    vca_lim_upper_r_i_arr = np.where(np.isclose(v_ca_a, vca_lim_upper_r, rtol=1e-2))[0]
    if vca_lim_upper_r_i_arr.size > 0:
        vca_lim_upper_r_i = vca_lim_upper_r_i_arr[0]
    else:
        vca_lim_upper_r_i = (np.abs(v_ca_a - vca_lim_upper_r)).argmin()
    
    # Ensure indices are valid and within bounds
    if vca_lim_lower_r_i >= len(v_ca_a) or vca_lim_upper_r_i >= len(v_ca_a) or \
       vca_lim_lower_r_i < 0 or vca_lim_upper_r_i < 0 or \
       vca_lim_lower_r_i == vca_lim_upper_r_i:
        logging.warning("Invalid indices for series resistance calculation. Returning default 0.0.")
        return 0.0

    delta_v = v_ca_a[vca_lim_upper_r_i] - v_ca_a[vca_lim_lower_r_i]
    delta_i = i_c_a[vca_lim_upper_r_i] - i_c_a[vca_lim_lower_r_i]

    if delta_i == 0:
        logging.warning("Change in current is zero for series resistance calculation. Returning default 0.0.")
        return 0.0

    r_ohm_simple = delta_v / delta_i
    logging.info(f'R_S_simple = {r_ohm_simple}')
    return r_ohm_simple

def diode_model_params_isotherm(v_ca_a, i_c_a , c_ca_a , T):
    """Extract HSPICE Level=3 compatible diode model parameters at a fixed temperature"""
    vca_lim_lower_ic = 0.65
    vca_lim_upper_ic = 0.75
    IS, N = ideal_diode_model(v_ca_a, i_c_a , vca_lim_lower_ic, vca_lim_upper_ic, T)

    print(f'Model parameters for T = {T:.0f} K: IS = {IS} A, N = {N}')

    vca_lim_lower_r = -100.0
    vca_lim_upper_r = 1.0
    RS = _extract_series_resistance(v_ca_a, i_c_a, vca_lim_lower_r, vca_lim_upper_r)

    BV, IBV = _extract_breakdown_parameters(v_ca_a, i_c_a, DEFAULT_IBV)

    vca_lim_lower_cca = -100.0
    vca_lim_upper_cca = 1.0
    # Now diode_capacitance_model extracts CJO, VJ, M, TT
    CJO, VJ, M, TT = diode_capacitance_model(v_ca_a, i_c_a , c_ca_a , vca_lim_lower_cca, vca_lim_upper_cca)

    logging.info(f'Transit time for T = {T:.0f} K: TT = {TT:.4g} s.')
    logging.info(f'CJO = {CJO:.4g}F, VJ = {VJ:.4g}V, M = {M:.4g}')

    if np.isinf(BV):
        BV = None # Convert np.inf to None for JSON compatibility

    # HSPICE Level=3 standard parameter dictionary
    model_params = {'T': T, 'IS': IS, 'N': N, 'RS': RS,
                    'TT': TT, 'CJO': CJO, 'VJ': VJ, 'M': M, 'FC': DEFAULT_FC,
                    'BV': BV, 'IBV': IBV, 'EG': SILICON_BANDGAP_VOLTAGE_EV, 'XTI': DEFAULT_XTI,
                    'vca_lim_lower_ic': vca_lim_lower_ic,
                    'vca_lim_upper_ic': vca_lim_upper_ic,
                    'vca_lim_lower_cca': vca_lim_lower_cca,
                    'vca_lim_upper_cca': vca_lim_upper_cca,
                    'vca_lim_lower_r': vca_lim_lower_r,
                    'vca_lim_upper_r': vca_lim_upper_r,
                    }
    return model_params