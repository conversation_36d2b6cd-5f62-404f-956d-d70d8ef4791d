#! /usr/bin/python3

# pylint: disable=unused-wildcard-import
import json
import numpy as np
import logging
from config_loader import load_config

# Use diode functions of Python files in working directory
from diode_modelling import DiodeModelIsotherm, DiodeModel, diode_model_params_isotherm, ideal_diode_model, diode_capacitance_model
from diode_equations import diode_saturation_current, diode_saturation_current_0, NOMINAL_TEMPERATURE_K, SILICON_BANDGAP_VOLTAGE_EV, DEFAULT_FC, DEFAULT_CJO, DEFAULT_VJ, DEFAULT_M_GRADING, DEFAULT_TT, DEFAULT_IBV
from dat_file_parser import load_measurement_data, detect_file_format


def _load_and_validate_measurements(measurements_fname):
    """Loads measurement data from JSON or DAT file and performs initial validation.

    Args:
        measurements_fname (str): Path to the measurement file (JSON or DAT format).

    Returns:
        tuple: (measurements, plot_data_list) if successful, (None, None) otherwise.
    """
    try:
        # Use the new universal data loader
        measurements = load_measurement_data(measurements_fname)
        file_format = detect_file_format(measurements_fname)
        logging.info(f"Successfully loaded {file_format.upper()} format measurement file: {measurements_fname}")

    except FileNotFoundError:
        logging.error(f"Measurement file '{measurements_fname}' not found.")
        return None, None
    except (json.JSONDecodeError, ValueError) as e:
        logging.error(f"Could not parse measurement file '{measurements_fname}': {e}")
        return None, None
    except Exception as e:
        logging.error(f"Unexpected error loading '{measurements_fname}': {e}")
        return None, None

    plot_data_list = []
    plot_data_list.append({
        'plot_type': 'overview',
        'meas_dict': measurements
    })

    # Add IV curve plots for each measurement immediately after loading
    _add_iv_curve_plots(measurements, plot_data_list)

    return measurements, plot_data_list


def _add_iv_curve_plots(measurements, plot_data_list):
    """Add simple IV data curve for immediate visualization after data loading."""
    logging.info("Adding IV data curve for loaded data...")

    for measurement_key, measurement_data in measurements.items():
        try:
            # Extract IV data using both possible key formats
            v_ca_a = None
            i_c_a = None

            # Try uppercase keys first (new format)
            if 'V_CA' in measurement_data and 'I_C' in measurement_data:
                v_ca_a = np.array(measurement_data['V_CA'])
                i_c_a = np.array(measurement_data['I_C'])
            # Fall back to lowercase keys (legacy format)
            elif 'v_ca_a' in measurement_data and 'i_c_a' in measurement_data:
                v_ca_a = np.array(measurement_data['v_ca_a'])
                i_c_a = np.array(measurement_data['i_c_a'])

            if v_ca_a is None or i_c_a is None:
                logging.warning(f"Skipping IV plot for {measurement_key}: missing voltage or current data")
                continue

            # Get temperature info
            temp_info = measurement_data.get('instance_params', {})
            temp_celsius = temp_info.get('T', 25.0)

            logging.info(f"Added IV data curve for {measurement_key}")

            # Add simple IV data to plot data list for GUI display
            plot_data_list.append({
                'plot_type': 'iv_data_only',
                'measurement_key': measurement_key,
                'v_ca_a': v_ca_a,
                'i_c_a': i_c_a,
                'temperature_C': temp_celsius
            })

        except Exception as e:
            logging.warning(f"Failed to add IV data for {measurement_key}: {e}")
            continue


def process_diode_measurement(measurements_fname='data.json',
    results_fname='model.json', progress_callback=None, selected_test_data=None):
    """Extract diode model parameters from JSON file with measurements.

    Structure of imported JSON File:
    Dict:
        meas_run(run_name, data) (tuple(string, dict)): Measurement run
        run_name (string): Name of test run, e.g. "T298.0K"
        data[phys_quantity: values] (dict[string: list]):
        Dictionary of diode capacitance ('C_CA'),
        current ('I_C) and voltage ('V_CA') values
    Args:
        measurements_fname (string): File name of a JSON file
        results_fname (string): File name for saving extracted model parameters.

    Returns:
        tuple: (plot_data_list, extracted_params)
            plot_data_list (list): A list of dictionaries, each containing data for a plot.
            extracted_params (dict): Dictionary of extracted model parameters.
    """
    if progress_callback: progress_callback(5)
    logging.info("Loading and validating measurements...")
    measurements, plot_data_list = _load_and_validate_measurements(measurements_fname)
    if measurements is None:
        if progress_callback: progress_callback(0)
        logging.error("Measurement loading failed.")
        return [], {}

    models = []
    i_s_temp_list = []
    T_i_s_list = []

    # Initialize reference variables
    v_ca_a_0 = None
    i_c_a_0 = None
    c_ca_a_0 = None
    T_0 = None

    if progress_callback: progress_callback(10)
    logging.info("Sorting measurements...")
    sorted_measurements = sorted(measurements.items(), key=lambda item: float(item[0][1:5]) if item[0].startswith('T') and len(item[0]) >= 5 else float('inf'))

    # 根据用户选择过滤测试数据
    if selected_test_data is not None:
        if selected_test_data == "nominal_only":
            # 只保留接近标称温度的数据
            filtered_measurements = []
            for measurement_key, measurement_value in sorted_measurements:
                try:
                    if measurement_key.startswith('T') and len(measurement_key) >= 5:
                        T = float(measurement_key[1:5])
                        if abs(T - NOMINAL_TEMPERATURE_K) < 10.0:  # 10K范围内
                            filtered_measurements.append((measurement_key, measurement_value))
                except ValueError:
                    continue
            sorted_measurements = filtered_measurements
            logging.info(f"Filtered to nominal temperature data only. Processing {len(sorted_measurements)} measurements.")
        elif isinstance(selected_test_data, list):
            # 只保留用户选择的测试数据
            filtered_measurements = []
            for measurement_key, measurement_value in sorted_measurements:
                if measurement_key in selected_test_data:
                    filtered_measurements.append((measurement_key, measurement_value))
            sorted_measurements = filtered_measurements
            logging.info(f"Filtered to selected test data: {selected_test_data}. Processing {len(sorted_measurements)} measurements.")
    else:
        logging.info(f"Processing all available measurements: {len(sorted_measurements)} measurements.")

    total_measurements = len(sorted_measurements)
    for i, (measurement_key, measurement_value) in enumerate(sorted_measurements):
        if progress_callback:
            progress = 10 + int((i / total_measurements) * 70) # 10% for loading, 70% for processing each measurement
            progress_callback(progress)
            logging.info(f"Processing measurement {measurement_key}...")
        try:
            # Validate measurement key format (e.g., "T298.0K")
            if not isinstance(measurement_key, str) or len(measurement_key) < 5 or measurement_key[0] != 'T':
                logging.warning(f"Skipping measurement '{measurement_key}' due to invalid measurement key format. Expected 'TXXX.XK'.")
                continue
            try:
                T = float(measurement_key[1:5])
            except ValueError:
                logging.warning(f"Skipping measurement '{measurement_key}' due to invalid temperature value. Expected 'TXXX.XK'.")
                continue

            # Validate measurement data keys (V_CA, I_C, C_CA)
            if not isinstance(measurement_value, dict) or not all(key in measurement_value for key in ['V_CA', 'I_C', 'C_CA']):
                logging.warning(f"Skipping measurement '{measurement_key}' due to missing or invalid data keys (V_CA, I_C, or C_CA).")
                continue

            v_ca_a = np.array(measurement_value['V_CA'][:])
            i_c_a  = np.array(measurement_value['I_C'][:])
            c_ca_a  =  np.array(measurement_value['C_CA'][:])

            model = DiodeModelIsotherm(v_ca_a, i_c_a , c_ca_a , T)

            # Add V_CA vs I_C plot data
            plot_data_list.append({
                'plot_type': 'vca_ic',
                'v_ca_a': v_ca_a,
                'i_c_a': i_c_a,
                'model': model
            })

            # Add V_CA vs C_CA plot data
            plot_data_list.append({
                'plot_type': 'vca_cca',
                'v_ca_a': v_ca_a,
                'i_c_a': i_c_a,
                'c_ca_a': c_ca_a,
                'model': model
            })
        except Exception as e:
            logging.error(f"Error processing measurement '{measurement_key}': {e}. Skipping this measurement.")
            continue

        models.append(model)
        i_s_0 = diode_saturation_current_0(model.i_s, T)
        logging.info(f'I_S = {model.i_s}, I_S0_model = {i_s_0}')
        i_s_model = diode_saturation_current(i_s_0, model.T)
        i_s_temp_list.append(model.i_s)
        T_i_s_list.append(model.T)
        logging.info(f' For T = {model.T}K: I_S = {model.i_s} A, I_S_model(T) = {i_s_model} A.')

        # Set reference data - prefer nominal temperature, but use first available if none match
        if (NOMINAL_TEMPERATURE_K - 10. < model.T < NOMINAL_TEMPERATURE_K + 10.):
            logging.info(f'Measurement series at T = {model.T}K has less than 10 K difference to T_nom = NOMINAL_TEMPERATURE_K and will be used as reference.')
            # Set variables for base measurements (T ~ T_0 = 300.15K)
            v_ca_a_0 = v_ca_a
            i_c_a_0 = i_c_a
            c_ca_a_0 = c_ca_a
            T_0 = model.T
        elif v_ca_a_0 is None:
            # Use first measurement as reference if no nominal temperature data available
            logging.info(f'Using measurement at T = {model.T}K as reference (no nominal temperature data available).')
            v_ca_a_0 = v_ca_a
            i_c_a_0 = i_c_a
            c_ca_a_0 = c_ca_a
            T_0 = model.T

        # Update the existing IV data plot to include model fitting
        if (NOMINAL_TEMPERATURE_K - 10. < model.T < NOMINAL_TEMPERATURE_K + 10.) or (v_ca_a_0 is v_ca_a):
            # Find and update the existing iv_data_only plot
            for i, plot_data in enumerate(plot_data_list):
                if plot_data.get('plot_type') == 'iv_data_only' and plot_data.get('measurement_key') == measurement_key:
                    # Update the plot type to include model fitting
                    plot_data_list[i] = {
                        'plot_type': 'iv_data_with_model',
                        'v_ca_a': v_ca_a,
                        'i_c_a': i_c_a,
                        'model': model,
                        'measurement_key': measurement_key,
                        'temperature_C': T - 273.15  # Convert K to C
                    }
                    break

    # Ensure reference data is available
    if v_ca_a_0 is None or i_c_a_0 is None or c_ca_a_0 is None:
        raise ValueError("No valid measurement data found for model creation")

    base_model = DiodeModel(v_ca_a_0, i_c_a_0 , c_ca_a_0 , T_0, np.array(T_i_s_list),
                            np.array(i_s_temp_list))
    # Note: Only IV fitting plots are generated, no temperature dependence plots

    if progress_callback: progress_callback(90)
    logging.info("Saving extracted parameters...")
    # Save extracted parameters
    try:
        with open(results_fname, 'w') as f:
            json.dump(base_model.params, f, ensure_ascii=True, indent=4) # Added indent=4
    except IOError:
        logging.error(f"Could not write results to '{results_fname}'. Check file permissions.")
        if progress_callback: progress_callback(0)
        logging.error("Error saving results.")
        return [], {}

    if progress_callback: progress_callback(100)
    logging.info("Extraction complete.")
    return plot_data_list, base_model.params


import argparse

def main():
    parser = argparse.ArgumentParser(description='Extract diode model parameters from I-V measurement data.')
    parser.add_argument('--data_file', type=str, help='Path to the input JSON data file.')
    parser.add_argument('--results_file', type=str, help='Path to the output JSON results file.')
    args = parser.parse_args()

    logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

    if args.data_file and args.results_file:
        data_fname = args.data_file
        results_fname = args.results_file
    else:
        config = load_config()
        if config is None:
            return
        data_fname = config.get('Paths', 'data_file', fallback='data.json')
        results_fname = config.get('Paths', 'results_file', fallback='model.json')

    plot_data, extracted_params = process_diode_measurement(data_fname, results_fname=results_fname)
    if extracted_params: # Only print if extraction was successful
        logging.info(f"Extracted Parameters: {extracted_params}")
    # In CLI mode, we don't plot, just print parameters.


# Do not execute main() when imported as module
if __name__ == '__main__':
    main()