#! /usr/bin/python3

# pylint: disable=unused-wildcard-import
import json
import numpy as np
import logging
from config_loader import load_config

# Use diode functions of Python files in working directory
from diode_modelling import DiodeModelIsotherm, DiodeModel, diode_model_params_isotherm, ideal_diode_model, diode_capacitance_model
from diode_equations import diode_saturation_current, diode_saturation_current_0, NOMINAL_TEMPERATURE_K, SILICON_BANDGAP_VOLTAGE_EV, DEFAULT_FC, DEFAULT_CJO, DEFAULT_VJ, DEFAULT_M_GRADING, DEFAULT_TT, DEFAULT_IBV


def _load_and_validate_measurements(measurements_fname):
    """Loads measurement data from a JSON file and performs initial validation.

    Args:
        measurements_fname (str): Path to the JSON file containing measurement data.

    Returns:
        tuple: (measurements, plot_data_list) if successful, (None, None) otherwise.
    """
    try:
        with open(measurements_fname, 'r') as f:
            measurements = json.load(f)
    except FileNotFoundError:
        logging.error(f"Measurement file '{measurements_fname}' not found.")
        return None, None
    except json.JSONDecodeError:
        logging.error(f"Could not decode JSON from '{measurements_fname}'. Check file format.")
        return None, None

    plot_data_list = []
    plot_data_list.append({
        'plot_type': 'overview',
        'meas_dict': measurements
    })
    return measurements, plot_data_list




def process_diode_measurement(measurements_fname='data.json',
    results_fname='model.json', progress_callback=None):
    """Extract diode model parameters from JSON file with measurements.

    Structure of imported JSON File:
    Dict:
        meas_run(run_name, data) (tuple(string, dict)): Measurement run
        run_name (string): Name of test run, e.g. "T298.0K"
        data[phys_quantity: values] (dict[string: list]):
        Dictionary of diode capacitance ('C_CA'),
        current ('I_C) and voltage ('V_CA') values
    Args:
        measurements_fname (string): File name of a JSON file
        results_fname (string): File name for saving extracted model parameters.

    Returns:
        tuple: (plot_data_list, extracted_params)
            plot_data_list (list): A list of dictionaries, each containing data for a plot.
            extracted_params (dict): Dictionary of extracted model parameters.
    """
    if progress_callback: progress_callback(5)
    logging.info("Loading and validating measurements...")
    measurements, plot_data_list = _load_and_validate_measurements(measurements_fname)
    if measurements is None:
        if progress_callback: progress_callback(0)
        logging.error("Measurement loading failed.")
        return [], {}

    models = []
    i_s_temp_list = []
    T_i_s_list = []

    if progress_callback: progress_callback(10)
    logging.info("Sorting measurements...")
    sorted_measurements = sorted(measurements.items(), key=lambda item: float(item[0][1:5]) if item[0].startswith('T') and len(item[0]) >= 5 else float('inf'))

    total_measurements = len(sorted_measurements)
    for i, (measurement_key, measurement_value) in enumerate(sorted_measurements):
        if progress_callback:
            progress = 10 + int((i / total_measurements) * 70) # 10% for loading, 70% for processing each measurement
            progress_callback(progress)
            logging.info(f"Processing measurement {measurement_key}...")
        try:
            # Validate measurement key format (e.g., "T298.0K")
            if not isinstance(measurement_key, str) or len(measurement_key) < 5 or measurement_key[0] != 'T':
                logging.warning(f"Skipping measurement '{measurement_key}' due to invalid measurement key format. Expected 'TXXX.XK'.")
                continue
            try:
                T = float(measurement_key[1:5])
            except ValueError:
                logging.warning(f"Skipping measurement '{measurement_key}' due to invalid temperature value. Expected 'TXXX.XK'.")
                continue

            # Validate measurement data keys (V_CA, I_C, C_CA)
            if not isinstance(measurement_value, dict) or not all(key in measurement_value for key in ['V_CA', 'I_C', 'C_CA']):
                logging.warning(f"Skipping measurement '{measurement_key}' due to missing or invalid data keys (V_CA, I_C, or C_CA).")
                continue

            v_ca_a = np.array(measurement_value['V_CA'][:])
            i_c_a  = np.array(measurement_value['I_C'][:])
            c_ca_a  =  np.array(measurement_value['C_CA'][:])

            model = DiodeModelIsotherm(v_ca_a, i_c_a , c_ca_a , T)

            # Add V_CA vs I_C plot data
            plot_data_list.append({
                'plot_type': 'vca_ic',
                'v_ca_a': v_ca_a,
                'i_c_a': i_c_a,
                'model': model
            })

            # Add V_CA vs C_CA plot data
            plot_data_list.append({
                'plot_type': 'vca_cca',
                'v_ca_a': v_ca_a,
                'i_c_a': i_c_a,
                'c_ca_a': c_ca_a,
                'model': model
            })
        except Exception as e:
            logging.error(f"Error processing measurement '{measurement_key}': {e}. Skipping this measurement.")
            continue

        models.append(model)
        i_s_0 = diode_saturation_current_0(model.i_s, T)
        logging.info(f'I_S = {model.i_s}, I_S0_model = {i_s_0}')
        i_s_model = diode_saturation_current(i_s_0, model.T)
        i_s_temp_list.append(model.i_s)
        T_i_s_list.append(model.T)
        logging.info(f' For T = {model.T}K: I_S = {model.i_s} A, I_S_model(T) = {i_s_model} A.')
        if (NOMINAL_TEMPERATURE_K - 10. < model.T < NOMINAL_TEMPERATURE_K + 10.):
            logging.info(f'Measurement series at T = {model.T}K has less than 10 K difference to T_nom = NOMINAL_TEMPERATURE_K and will be used as reference.')
            # Set variables for base measurements (T ~ T_0 = 300.15K)
            v_ca_a_0 = v_ca_a
            i_c_a_0 = i_c_a
            c_ca_a_0 = c_ca_a

            # Add presentation plots data
            plot_data_list.append({
                'plot_type': 'vca_cca_for_presentation',
                'v_ca_a': v_ca_a,
                'i_c_a': i_c_a,
                'c_ca_a': c_ca_a,
                'model': model
            })
            plot_data_list.append({
                'plot_type': 'vca_ic_ideal',
                'v_ca_a': v_ca_a,
                'i_c_a': i_c_a,
                'model': model
            })
            plot_data_list.append({
                'plot_type': 'vca_ic_r',
                'v_ca_a': v_ca_a,
                'i_c_a': i_c_a,
                'model': model
            })

    base_model = DiodeModel(v_ca_a_0, i_c_a_0 , c_ca_a_0 , T, np.array(T_i_s_list),
                            np.array(i_s_temp_list))
    # Add T_is plot data
    plot_data_list.append({
        'plot_type': 'T_is',
        'T_a': np.array(T_i_s_list),
        'i_s_a': np.array(i_s_temp_list),
        'model': base_model
    })

    if progress_callback: progress_callback(90)
    logging.info("Saving extracted parameters...")
    # Save extracted parameters
    try:
        with open(results_fname, 'w') as f:
            json.dump(base_model.params, f, ensure_ascii=True, indent=4) # Added indent=4
    except IOError:
        logging.error(f"Could not write results to '{results_fname}'. Check file permissions.")
        if progress_callback: progress_callback(0)
        logging.error("Error saving results.")
        return [], {}

    if progress_callback: progress_callback(100)
    logging.info("Extraction complete.")
    return plot_data_list, base_model.params


import argparse

def main():
    parser = argparse.ArgumentParser(description='Extract diode model parameters from I-V measurement data.')
    parser.add_argument('--data_file', type=str, help='Path to the input JSON data file.')
    parser.add_argument('--results_file', type=str, help='Path to the output JSON results file.')
    args = parser.parse_args()

    logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

    if args.data_file and args.results_file:
        data_fname = args.data_file
        results_fname = args.results_file
    else:
        config = load_config()
        if config is None:
            return
        data_fname = config.get('Paths', 'data_file', fallback='data.json')
        results_fname = config.get('Paths', 'results_file', fallback='model.json')

    plot_data, extracted_params = process_diode_measurement(data_fname, results_fname=results_fname)
    if extracted_params: # Only print if extraction was successful
        logging.info(f"Extracted Parameters: {extracted_params}")
    # In CLI mode, we don't plot, just print parameters.


# Do not execute main() when imported as module
if __name__ == '__main__':
    main()