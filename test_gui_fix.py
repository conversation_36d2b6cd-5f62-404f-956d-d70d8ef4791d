#!/usr/bin/env python3
"""
测试脚本：验证GUI修复是否成功
"""

import sys
import os
import json
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_gui_creation():
    """测试GUI是否能正常创建"""
    try:
        from diode_pyqt_gui import DiodeParameterExtractorGUI
        
        app = QApplication(sys.argv)
        window = DiodeParameterExtractorGUI()
        
        # 检查关键属性是否存在
        assert hasattr(window, 'param_widgets'), "param_widgets属性缺失"
        assert hasattr(window, '_updating_gui'), "_updating_gui属性缺失"
        assert hasattr(window, 'param_keys'), "param_keys属性缺失"
        
        # 检查参数控件是否正确创建
        expected_params = ["I_<PERSON>", "m", "R_S", "CJO", "VJ", "M_GRADING", "FC"]
        for param in expected_params:
            assert param in window.param_widgets, f"参数 {param} 的控件未创建"
            assert 'line_edit' in window.param_widgets[param], f"参数 {param} 缺少line_edit"
            assert 'slider' in window.param_widgets[param], f"参数 {param} 缺少slider"
        
        print("✅ GUI创建测试通过")
        
        # 测试参数设置
        test_value = 1e-10
        if "I_S" in window.param_widgets:
            window.param_widgets["I_S"]['line_edit'].setText(f"{test_value:.4e}")
            text = window.param_widgets["I_S"]['line_edit'].text()
            assert text == f"{test_value:.4e}", f"参数设置失败: 期望 {test_value:.4e}, 实际 {text}"
        
        print("✅ 参数控件测试通过")
        
        # 关闭应用
        app.quit()
        return True
        
    except Exception as e:
        print(f"❌ GUI测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_core_functionality():
    """测试核心功能是否正常"""
    try:
        from extract_diode_model_parameters import process_diode_measurement
        
        # 检查数据文件是否存在
        if not os.path.exists('data.json'):
            print("⚠️  警告: data.json文件不存在，跳过核心功能测试")
            return True
            
        print("🔄 测试核心参数提取功能...")
        
        # 测试参数提取（使用较小的数据集）
        plot_data, params = process_diode_measurement('data.json', 'test_model.json')
        
        assert isinstance(params, dict), "返回的参数不是字典类型"
        assert len(params) > 0, "没有提取到任何参数"
        
        expected_keys = ['I_S', 'm', 'R_S', 'T']
        for key in expected_keys:
            if key in params:
                assert isinstance(params[key], (int, float)), f"参数 {key} 不是数值类型"
        
        print("✅ 核心功能测试通过")
        
        # 清理测试文件
        if os.path.exists('test_model.json'):
            os.remove('test_model.json')
            
        return True
        
    except Exception as e:
        print(f"❌ 核心功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始GUI修复验证测试...")
    print("=" * 50)
    
    # 测试GUI创建
    gui_test_passed = test_gui_creation()
    
    # 测试核心功能
    core_test_passed = test_core_functionality()
    
    print("=" * 50)
    if gui_test_passed and core_test_passed:
        print("🎉 所有测试通过！GUI修复成功！")
        print("\n📋 使用说明:")
        print("1. 运行 'python diode_pyqt_gui.py' 启动GUI")
        print("2. 或运行 'python extract_diode_model_parameters.py' 使用命令行版本")
        return 0
    else:
        print("❌ 部分测试失败，请检查错误信息")
        return 1

if __name__ == "__main__":
    sys.exit(main())
