""" Plots of Diode behavior """
import numpy as np
from diode_equations import  calc_rd_deriv_a
import logging



def plot_measurements_overview(meas_dict, ax_ic, ax_cca):
    """Plot all measured data as an overview

        meas_run(run_name, data) (tuple(string, dict)): Measurement run
        run_name (string): Name of test run, e.g. "T298.0K"
        data[phys_quantity: values] (dict[string: list]):
        Dictionary of diode capacitance ('C_CA'),
        current ('I_C) and voltage ('V_CA') values
    Args:
        measurement_dict (dict):
            meas_run(run_name, data) (tuple(string, dict)): Measurement run
            run_name (string): Name of test run, e.g. "T298.0K"
            data[phys_quantity: values] (dict[string: list]):
            Dictionary of diode capacitance ('C_CA'),
            current ('I_C) and voltage ('V_CA') values
        ax_ic (matplotlib.axes.Axes): Axes object for I_C vs V_CA plot.
        ax_cca (matplotlib.axes.Axes): Axes object for C_CA vs V_CA plot.
    """
    # Plot I_C over V_CA for all measurement runs
    ax_ic.set_xlabel('V_CA [V]')
    ax_ic.set_ylabel('I_C[A]')
    ax_ic.set_title('I_C vs V_CA Overview')
    labelnames = []
    for meas in meas_dict.items():
        v_ca_a = meas[1]['V_CA'][:]
        i_c_a  = meas[1]['I_C'][:]
        label_temp = meas[0]
        ax_ic.semilogy(v_ca_a, i_c_a, '-', label=label_temp)
        labelnames.append(label_temp)
    ax_ic.legend(labelnames, loc='best')
    ax_ic.grid(True)

    # Plot V_CA over V_CA for all measurement runs
    ax_cca.set_xlabel('V_CA [V]')
    ax_cca.set_ylabel('C_CA [F]')
    ax_cca.set_title('C_CA vs V_CA Overview')
    labelnames = []
    for meas in meas_dict.items():
        v_ca_a = meas[1]['V_CA'][:]
        c_ca_a  =  meas[1]['C_CA'][:]
        label_temp = meas[0]
        ax_cca.plot(v_ca_a, c_ca_a, '-', label=label_temp)
        labelnames.append(label_temp)
    ax_cca.legend(labelnames, loc='best')
    ax_cca.grid(True)


def plot_vca_cca(v_ca_a, i_c_a , c_ca, model, ax1, ax2):
    """Plot diode capacitance over diode voltage

    Args:
        v_ca_a (float array): Cathode-Anode voltage.
        i_c_a  (float array): Diode current.
        c_ca_a (float array): Diode capacitance.
        model (DiodeModelIsotherm): Diode model object.
        ax1 (matplotlib.axes.Axes): Axes object for I_C vs V_CA plot.
        ax2 (matplotlib.axes.Axes): Axes object for C_CA vs V_CA plot.
    """
    i_c_r_a  = model.calc_ic_diode_ohmic_a(v_ca_a)
    c_ca_model = model.calc_c_diode_a(i_c_r_a )

    # Plot IC over V_CA
    label_ic = ax1.plot(v_ca_a, i_c_a , '.', label='I_C')
    ax1.set_ylabel('I_C [A]')
    ax1.set_xlabel('V_CA [V]')
    ax1.set_title(f'I_C and C_CA vs V_CA at T={model.T}K')

    # Prepare Labels of ax1
    labelnames =  label_ic
    labels = [l.get_label() for l in labelnames]
    ax1.legend(labelnames, labels, loc='best')
    ax1.grid(True)

    # Plot C_CA over V_CA
    label_cca = ax2.plot(v_ca_a, c_ca, 'rx', label='C_CA')
    lab_cca_model = ax2.plot(v_ca_a, c_ca_model, 'g-',
                             label=model.label_cca_model)
    ax2.set_ylabel('C_CA [F]')

    # Prepare Labels of ax2
    labelnames =  label_cca + lab_cca_model
    labels = [l.get_label() for l in labelnames]
    ax2.legend(labelnames, labels, loc='lower right')
    ax2.grid(True)


def plot_vca_ic(v_ca_a, i_c_a , model, ax1, axr):
    """Plot diode current over diode voltage

    Args:
        v_ca_a (float array): Cathode-Anode voltage.
        i_c_a  (float array): Diode current.
        model (DiodeModelIsotherm): Diode model object.
        ax1 (matplotlib.axes.Axes): Axes object for I_C vs V_CA plot.
        axr (matplotlib.axes.Axes): Twin Axes object for R vs V_CA plot.
    """
    # Calculate the model data
    i_c_ideal_diode_model_a = model.calc_ic_ideal_diode_a(v_ca_a)
    r_D_a = calc_rd_deriv_a(v_ca_a, i_c_a )
    i_c_r_a  = model.calc_ic_diode_ohmic_a(v_ca_a)

    # Plot I_C and models over V_CA
    label_ic = ax1.semilogy(v_ca_a, i_c_a , '.', label='I_C')
    label_ic_model = ax1.semilogy(v_ca_a, i_c_ideal_diode_model_a, '-b',
                                  label=model.label_ideal_diode_model)
    label_ic_r_model = ax1.semilogy(v_ca_a, i_c_r_a , '--r',
                                    label=model.label_diode_ohmic)
    ax1.set_xlabel('V_CA [V]')
    ax1.set_ylabel('I_C  [A]')
    ax1.set_title(f'I_C and R vs V_CA at T={model.T}K')

    # Prepare I_C Labels of ax1
    labelnames =  label_ic + label_ic_model + label_ic_r_model
    labels = [l.get_label() for l in labelnames]
    ax1.legend(labelnames, labels, loc='best')
    ax1.grid(True)

    # Plot r_D over V_CA
    axr.set_ylabel('R [$\Omega$]')
    axr.set_ylim([0, 10])
    label_r = axr.plot(v_ca_a, r_D_a, 'g-.', label=model.label_r)
    axr.legend(loc='lower right')

    # Mark sections that form the basis of the i_c_r model
    ax1.axvspan(model.vca_lim_lower_ic, model.vca_lim_upper_ic, color='b', alpha=0.3)
    ax1.axvspan(model.vca_lim_lower_r, model.vca_lim_upper_r, color='g', alpha=0.3)


def plot_vca_cca_for_presentation(v_ca_a, i_c_a , c_ca, model, ax1, ax2):
    """Plot diode capacitance over diode voltage for presentation

    Args:
        v_ca_a (float array): Cathode-Anode voltage.
        i_c_a  (float array): Diode current.
        c_ca_a (float array): Diode capacitance.
        model (DiodeModelIsotherm): Diode model object.
        ax1 (matplotlib.axes.Axes): Axes object for I_C vs V_CA plot.
        ax2 (matplotlib.axes.Axes): Axes object for C_CA vs V_CA plot.
    """
    i_c_r_a  = model.calc_ic_diode_ohmic_a(v_ca_a)
    c_ca_model = model.calc_c_diode_a(i_c_r_a )

    # Plot IC over V_CA
    label_ic = ax1.plot(v_ca_a, i_c_a , '.', label='I_C')
    ax1.set_ylabel('I_C [A]')
    ax1.set_xlabel('V_CA [V]')
    ax1.set_title(f'I_C and C_CA vs V_CA at T={model.T}K (Presentation)')

    # Prepare Labels of ax1
    labelnames =  label_ic
    labels = [l.get_label() for l in labelnames]
    ax1.legend(labelnames, labels, loc='best')
    ax1.grid(True)

    # Plot C_CA over V_CA
    label_cca = ax2.plot(v_ca_a, c_ca, 'rx', label='C_CA(measured)')
    lab_cca_model = ax2.plot(v_ca_a, c_ca_model, 'g-',
                             label='Curve Fitted Model C_d')
    ax2.set_ylabel('C_CA [F]')

    # Prepare Labels of ax2
    labelnames =  label_cca + lab_cca_model
    labels = [l.get_label() for l in labelnames]
    ax2.legend(labelnames, labels, loc='lower right')
    ax2.grid(True)


def plot_vca_ic_ideal(v_ca_a, i_c_a , model, ax1):
    """Plot diode current over diode voltage (ideal)

    Args:
        v_ca_a (float array): Cathode-Anode voltage.
        i_c_a  (float array): Diode current.
        model (DiodeModelIsotherm): Diode model object.
        ax1 (matplotlib.axes.Axes): Axes object for the plot.
    """
    log_vector = np.vectorize(np.log)
    i_c_a_log = log_vector(i_c_a)

    # Calculate the model data
    v_0_to_1 = np.linspace(0, 1.0, 201)
    i_c_ideal_diode_model_a = model.calc_ic_ideal_diode_a(v_0_to_1)
    i_c_ideal_diode_model_a_log = log_vector(i_c_ideal_diode_model_a)

    # Plot I_C and models over V_CA
    label_ic = ax1.plot(v_ca_a, i_c_a_log , '.', label='Data')
    label_ic_model = ax1.plot(v_0_to_1, i_c_ideal_diode_model_a_log, '-r',
                                  label='Model')
    ax1.set_xlabel('U_D [V]')
    ax1.set_ylabel('ln(I_D)  [A]')
    ax1.set_title(f'Ideal Diode I-V at T={model.T}K')

    # Prepare I_C Labels of ax1
    labelnames =  label_ic + label_ic_model
    labels = [l.get_label() for l in labelnames]
    ax1.legend(labelnames, labels, loc='best')
    ax1.grid(True)


def plot_vca_ic_r(v_ca_a, i_c_a , model, ax1, axr):
    """Plot diode current over diode voltage (with resistance)

    Args:
        v_ca_a (float array): Cathode-Anode voltage.
        i_c_a  (float array): Diode current.
        model (DiodeModelIsotherm): Diode model object.
        ax1 (matplotlib.axes.Axes): Axes object for I_D vs U_D plot.
        axr (matplotlib.axes.Axes): Twin Axes object for R vs U_D plot.
    """
    # Calculate the model data
    r_D_a = calc_rd_deriv_a(v_ca_a, i_c_a )

    # Plot I_C and models over V_CA
    label_ic = ax1.plot(v_ca_a, i_c_a , '.', label='I_D (measured)')
    ax1.set_xlabel('U_D [V]')
    ax1.set_ylabel('I_D  [A]')
    ax1.set_title(f'Diode I-V with Resistance at T={model.T}K')

    # Prepare I_C Labels of ax1
    labelnames =  label_ic
    labels = [l.get_label() for l in labelnames]
    ax1.legend(labelnames, labels, loc='best')
    ax1.grid(True)

    # Plot r_D over V_CA
    axr.set_ylabel('R [$\Omega$]')
    axr.set_ylim([0, 10])
    label_r = axr.plot(v_ca_a, r_D_a, 'g-.', label=' r_D = dU_D/dI_D')
    axr.legend(loc='lower right')


def plot_T_is(T_a, i_s_a, model, ax1):
    """Plot diode saturation current over temperature

    Args:
        T_a (float array): Temperatures.
        i_s_a (float array): Saturation currents.
        model (DiodeModel): Diode model object.
        ax1 (matplotlib.axes.Axes): Axes object for the plot.
    """
    # Calculate the model data
    T_model_a, i_s_model_a = model.calc_i_s_temp_a()

    # Plot I_C and models over V_CA
    label_is = ax1.semilogy(T_a, i_s_a, 'xr', label='I_S (determined separately for each temperatures)')
    label_is_model = ax1.semilogy(T_model_a, i_s_model_a, '-b',
                                  label='I_S(T) model')
    ax1.set_xlabel('T [K]')
    ax1.set_ylabel('I_S [A]')
    ax1.set_title('I_S vs Temperature')

    # Prepare I_C Labels of ax1
    labelnames =  label_is + label_is_model
    labels = [l.get_label() for l in labelnames]
    ax1.legend(labelnames, labels, loc='best')
    ax1.grid(True)