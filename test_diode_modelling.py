
import unittest
import numpy as np
from diode_modelling import Diode<PERSON>odel, diode_model_params_isotherm
from diode_equations import ideal_diode_eq, ic_diode_ohmic, diode_saturation_current_standard, ic_diode_complete
from diode_modelling import total_capacitance_eq

class TestDiodeModelling(unittest.TestCase):

    def test_diode_model_params_isotherm(self):
        # This test remains to check the basic isotherm extraction
        T = 300.0
        i_s_known = 1e-12
        m_known = 1.1
        r_s_known = 0.01
        tt_known = 1e-9
        cjo_known = 1e-12
        vj_known = 0.7
        m_grading_known = 0.5
        fc_known = 0.5

        v_ca_a = np.linspace(0.6, 1, 200)
        i_c_a = ideal_diode_eq(v_ca_a, i_s_known, m_known, T)
        c_ca_a = total_capacitance_eq(v_ca_a, cjo_known, vj_known, m_grading_known, fc_known, tt_known, i_c_a)

        i_c_a += np.random.normal(0, 1e-15, i_c_a.shape)
        c_ca_a += np.random.normal(0, 1e-18, c_ca_a.shape)

        extracted_params = diode_model_params_isotherm(v_ca_a, i_c_a, c_ca_a, T)

        self.assertAlmostEqual(extracted_params['I_S'], i_s_known, delta=i_s_known * 0.5)
        self.assertAlmostEqual(extracted_params['m'], m_known, delta=m_known * 0.1)
        

    def test_diode_model_full_level3(self):
        # 1. Generate synthetic data with known Level 3 parameters
        T_nom = 300.15
        i_s_nom_known = 1.5e-12
        m_known = 1.05
        r_s_known = 0.02
        bv_known = 20.0
        ibv_known = 1e-3
        eg_known = 1.12
        xti_known = 2.9

        # I-V data with breakdown
        v_ca_a = np.linspace(-20, 1.0, 1000)
        i_c_a = ic_diode_complete(v_ca_a, i_s_nom_known, m_known, T_nom, r_s_known, bv_known, ibv_known)
        i_c_a += np.random.normal(0, 1e-7, i_c_a.shape)
        # Dummy capacitance data for this test
        c_ca_a = np.zeros_like(v_ca_a)

        # Temperature dependent data
        T_a = np.linspace(250, 400, 10)
        i_s_temp_a = diode_saturation_current_standard(i_s_nom_known, T_a, T_nom, eg_known, xti_known)
        i_s_temp_a += np.random.normal(0, 1e-15, i_s_temp_a.shape)

        # 2. Create a DiodeModel instance to trigger all extractions
        # We pass the data for the nominal temperature T_nom to the constructor
        model = DiodeModel(v_ca_a, i_c_a, c_ca_a, T_nom, T_a, i_s_temp_a)

        # 3. Assert that the extracted parameters are close to the known ones
        self.assertAlmostEqual(model.params['BV'], bv_known, delta=bv_known * 0.1)
        self.assertAlmostEqual(model.params['EG'], eg_known, delta=eg_known * 0.1)
        self.assertAlmostEqual(model.params['XTI'], xti_known, delta=xti_known * 0.1)

if __name__ == '__main__':
    unittest.main()
