""" Physics equations describing diode behavior
"""
import numpy as np
from scipy.optimize import curve_fit
import scipy.constants as const
from scipy.special import lambertw
from diode_utils import *
from config_loader import load_config

_config = load_config()

_config = load_config()

_config = load_config()

_config = load_config()

_config = load_config()

_config = load_config()

# --- HSPICE Level=3 Diode Model Constants ---
if _config and 'ModelDefaults' in _config:
    NOMINAL_TEMPERATURE_K = _config.getfloat('ModelDefaults', 'nominal_temperature_k', fallback=300.15)
    SILICON_BANDGAP_VOLTAGE_EV = _config.getfloat('ModelDefaults', 'silicon_bandgap_voltage_ev', fallback=1.17)
    DEFAULT_FC = _config.getfloat('ModelDefaults', 'default_fc', fallback=0.5)
    DEFAULT_CJO = _config.getfloat('ModelDefaults', 'default_cjo', fallback=1e-12)
    DEFAULT_VJ = _config.getfloat('ModelDefaults', 'default_vj', fallback=0.7)
    DEFAULT_M = _config.getfloat('ModelDefaults', 'default_m', fallback=0.5)  # HSPICE Level=3: M (grading coefficient)
    DEFAULT_TT = _config.getfloat('ModelDefaults', 'default_tt', fallback=1e-9)
    DEFAULT_IBV = _config.getfloat('ModelDefaults', 'default_ibv', fallback=1e-3)
    DEFAULT_XTI = _config.getfloat('ModelDefaults', 'default_xti', fallback=3.0)
    DEFAULT_EG = _config.getfloat('ModelDefaults', 'default_eg', fallback=1.17)  # HSPICE Level=3: EG (energy gap)
    DEFAULT_IS = _config.getfloat('ModelDefaults', 'default_is', fallback=1e-14)  # HSPICE Level=3: IS (saturation current)
    DEFAULT_N = _config.getfloat('ModelDefaults', 'default_n', fallback=1.0)     # HSPICE Level=3: N (ideality factor)
    DEFAULT_RS = _config.getfloat('ModelDefaults', 'default_rs', fallback=0.0)   # HSPICE Level=3: RS (series resistance)
    DEFAULT_BV = _config.getfloat('ModelDefaults', 'default_bv', fallback=1e6)   # HSPICE Level=3: BV (breakdown voltage)
else:
    # Fallback to hardcoded defaults if config is not available or section is missing
    NOMINAL_TEMPERATURE_K = 300.15
    SILICON_BANDGAP_VOLTAGE_EV = 1.17
    DEFAULT_FC = 0.5
    DEFAULT_CJO = 1e-12
    DEFAULT_VJ = 0.7
    DEFAULT_M = 0.5      # HSPICE Level=3: M (grading coefficient)
    DEFAULT_TT = 1e-9
    DEFAULT_IBV = 1e-3
    DEFAULT_XTI = 3.0
    DEFAULT_EG = 1.17    # HSPICE Level=3: EG (energy gap)
    DEFAULT_IS = 1e-14   # HSPICE Level=3: IS (saturation current)
    DEFAULT_N = 1.0      # HSPICE Level=3: N (ideality factor)
    DEFAULT_RS = 0.0     # HSPICE Level=3: RS (series resistance)
    DEFAULT_BV = 1e6     # HSPICE Level=3: BV (breakdown voltage)

# Legacy aliases for backward compatibility
DEFAULT_M_GRADING = DEFAULT_M

# --- Diode Equations ---

def ideal_diode_eq(v_ca: float, IS: float, N: float, T: float) -> float:
    """Shockley Diode equation (HSPICE Level=3 compatible)

    Args:
        v_ca (float): Cathode-Anode voltage [V].
        IS (float): Saturation current [A].
        N (float): Ideality factor.
        T (float): Temperature [K].

    Returns:
        float: Diode current I_C [A]
    """
    i_c = IS * (np.exp((v_ca * const.e)/(N * const.k * T)) - 1)
    return i_c

def get_ideal_diode_eq_log(T: float):
    """Returns a function for the logarithmic Shockley Diode equation,
    suitable for curve fitting (HSPICE Level=3 compatible).

    The returned function calculates log(I_C) based on V_CA, log(IS), and N.
    It assumes V_CA >> V_T, simplifying the Shockley equation to:
    I_C approxeq IS * exp(V_CA / (V_T * N))
    Taking the natural logarithm:
    log(I_C) approxeq log(IS) + V_CA / (V_T * N)

    Args:
        T (float): Temperature in Kelvin.

    Returns:
        Callable[[float, float, float], float]: A function that takes
            (v_ca, IS_log, N) and returns log(i_c).
    """
    def _ideal_diode_eq_log_internal(v_ca: float, IS_log: float, N: float) -> float:
        """Internal function for the logarithmic Shockley Diode equation.

        Args:
            v_ca (float): Cathode-Anode voltage [V].
            IS_log (float): Natural logarithm of the saturation current [ln(A)].
            N (float): Ideality factor.

        Returns:
            float: Natural logarithm of the diode current [ln(A)].
        """
        # Thermal voltage V_T = k * T / q
        # The term V_CA / (V_T * N) can be rewritten as (V_CA * q) / (k * T * N)
        i_c_log = IS_log + v_ca * (const.e / (N * const.k * T))
        return i_c_log
    return _ideal_diode_eq_log_internal


def ic_diode_complete(v_ca: np.ndarray, IS: float, N: float, T: float, RS: float, BV: float, IBV: float) -> np.ndarray:
    """Calculate current of a diode with series resistance and reverse breakdown (HSPICE Level=3 compatible).

    Args:
        v_ca (float): Cathode-Anode voltage [V].
        IS (float): Saturation current [A].
        N (float): Ideality factor.
        T (float): Temperature [K].
        RS (float): Ohmic diode resistance [Ohm].
        BV (float): Reverse breakdown voltage [V].
        IBV (float): Current at breakdown voltage [A].

    Returns:
        i_c (float): Diode current [A].
    """
    # Forward current component (same as ic_diode_ohmic)
    v_t = (const.k * T * N) / const.e
    i_c_forward = ((-IS * RS + v_t * lambertw(IS * RS * np.exp((IS * RS + v_ca) / v_t) / v_t)) / RS)
    i_c_forward = np.real(i_c_forward)

    # Reverse breakdown current component
    # A simple exponential model for breakdown
    i_c_breakdown = -IBV * np.exp(-(v_ca + BV) / (N * v_t))

    return i_c_forward + i_c_breakdown

def diode_saturation_current_standard(IS_nom: float, T: float, T_nom: float, EG: float, XTI: float) -> float:
    """Standard SPICE model for temperature dependent saturation current (HSPICE Level=3 compatible).

    Args:
        IS_nom (float): Saturation current at nominal temperature T_nom [A].
        T (float): Temperature [K].
        T_nom (float): Nominal temperature [K].
        EG (float): Energy gap [eV].
        XTI (float): Saturation current temperature exponent.

    Returns:
        float: IS(T) [A]
    """
    k_boltz = const.k
    q_electron = const.e

    vt_nom = k_boltz * T_nom / q_electron
    vt_t = k_boltz * T / q_electron

    factor1 = (T / T_nom)**XTI # Standard SPICE temperature dependence
    factor2 = np.exp(((EG / vt_nom) * (T / T_nom - 1)))

    IS_t = IS_nom * factor1 * factor2
    return IS_t



# Removed ohmic_resistance_diode as it was marked for deletion


def ic_diode_ohmic(v_ca: np.ndarray, IS: float, N: float, T: float, RS: float) -> np.ndarray:
    """Calculate current of an ideal diode in series with a resistor (HSPICE Level=3 compatible)

    Args:
        v_ca (float): Cathode-Anode voltage [V].
        IS (float): Saturation current [A].
        N (float): Ideality factor (model parameter)
        T (float): Temperature [K], defaults to 298.0
        RS (float): Ohmic diode resistance

    Returns:
        i_c_a  (float array): Diode current
    """
    # Ideal diode and ohmic resistance in series:
    # ln((i_c+IS)/IS) * v_t + i_c*RS -v_ca = 0
    # sympy.solve(log((x+a)/a)*b+c*x-d, x) = [(-a*c +
    #       b*LambertW(a*c*exp((a*c + d)/b)/b))/c]
    v_t = (const.k * T * N) / const.e
    i_c = ((-IS * RS + v_t * lambertw(IS*RS * np.exp((IS*RS + v_ca)/v_t)/v_t))/RS)
    i_c = np.real(i_c)      # Avoid warning; imaginary part is already zero
    return i_c


def ic_diode_spice_level3(v_ca: np.ndarray, IS: float, N: float, T: float, RS: float = 0.0,
                         BV: float = None, IBV: float = 1e-3, JTUN: float = None,
                         JTUNSW: float = None, NTUN: float = None, GMIN: float = 1e-12) -> np.ndarray:
    """Complete SPICE Level=3 diode current model following standard SPICE equations

    Total current: I_D = I_diff + I_tun

    Args:
        v_ca: Cathode-Anode voltage [V] (VD in SPICE notation)
        IS: Saturation current [A]
        N: Ideality factor (emission coefficient)
        T: Temperature [K]
        RS: Series resistance [Ohm]
        BV: Breakdown voltage [V] (positive value)
        IBV: Current at breakdown voltage [A]
        JTUN: Tunneling current density [A]
        JTUNSW: Sidewall tunneling current density [A]
        NTUN: Tunneling emission coefficient (default 30)
        GMIN: Convergence conductance [S] (default 1e-12)

    Returns:
        i_c_a: Total diode current [A]
    """
    v_t = const.k * T / const.e  # Thermal voltage

    # Handle None parameters with defaults
    if JTUN is None:
        JTUN = 0.0
    if JTUNSW is None:
        JTUNSW = 0.0
    if NTUN is None:
        NTUN = 30.0
    if RS is None:
        RS = 0.0
    if IBV is None:
        IBV = 1e-3
    if GMIN is None:
        GMIN = 1e-12

    # Initialize current array
    i_total = np.zeros_like(v_ca)

    # 1. Diffusion Current (I_diff) - Standard SPICE Level=3 Model
    i_diff = np.zeros_like(v_ca)

    # Define voltage regions according to SPICE standard
    if BV is not None and BV > 1.0:
        # Region boundaries
        v_breakdown_limit = -5 * N * v_t  # -5*N*Vt threshold

        # Region 1: VD >= -5*N*Vt (normal forward and moderate reverse)
        mask1 = v_ca >= v_breakdown_limit
        if np.any(mask1):
            if RS > 0:
                # Use Lambert W for series resistance
                try:
                    i_diff[mask1] = ic_diode_ohmic(v_ca[mask1], IS, N, T, RS)
                    # Add GMIN conductance
                    i_diff[mask1] += GMIN * v_ca[mask1]
                except:
                    # Fallback to exponential
                    exp_arg = np.clip(v_ca[mask1] / (N * v_t), -50, 50)
                    i_diff[mask1] = IS * (np.exp(exp_arg) - 1) + GMIN * v_ca[mask1]
            else:
                exp_arg = np.clip(v_ca[mask1] / (N * v_t), -50, 50)
                i_diff[mask1] = IS * (np.exp(exp_arg) - 1) + GMIN * v_ca[mask1]

        # Region 2: -BV < VD < -5*N*Vt (deep reverse, linear)
        mask2 = (v_ca > -BV) & (v_ca < v_breakdown_limit)
        if np.any(mask2):
            i_diff[mask2] = -IS + GMIN * v_ca[mask2]

        # Region 3: VD = -BV (breakdown point)
        mask3 = np.abs(v_ca + BV) < 1e-6  # Numerical tolerance
        if np.any(mask3):
            i_diff[mask3] = -IBV

        # Region 4: VD < -BV (beyond breakdown)
        mask4 = v_ca < -BV
        if np.any(mask4):
            exp_arg = np.clip((-BV + v_ca[mask4]) / v_t, -50, 50)
            i_diff[mask4] = -IS * (np.exp(exp_arg) - 1 + BV / v_t)
    else:
        # No breakdown - simple exponential model
        if RS > 0:
            try:
                i_diff = ic_diode_ohmic(v_ca, IS, N, T, RS)
                i_diff += GMIN * v_ca
            except:
                exp_arg = np.clip(v_ca / (N * v_t), -50, 50)
                i_diff = IS * (np.exp(exp_arg) - 1) + GMIN * v_ca
        else:
            exp_arg = np.clip(v_ca / (N * v_t), -50, 50)
            i_diff = IS * (np.exp(exp_arg) - 1) + GMIN * v_ca

    i_total += i_diff

    # 2. Tunneling Current (I_tun) - SPICE Level=3 Extension
    if (JTUN is not None and JTUN > 0) or (JTUNSW is not None and JTUNSW > 0):
        i_tun = np.zeros_like(v_ca)

        # Tunneling characteristic voltage
        v_tun = NTUN * v_t

        # Assume VB (band voltage) = 0 for simplicity, or could be a parameter
        VB = 0.0

        # Forward tunneling current (junction area)
        if JTUN > 0:
            # I_tun = JTUN * (VD - VB) * exp(-|VB| / (NTUN * Vt))
            # For VB = 0: I_tun = JTUN * VD * exp(0) = JTUN * VD
            # But this would be linear, so use more realistic tunneling model:
            exp_arg = np.clip(-np.abs(v_ca) / v_tun, -50, 50)
            i_tun += JTUN * (v_ca - VB) * np.exp(exp_arg)

        # Sidewall tunneling current
        if JTUNSW > 0:
            # Similar model for sidewall
            exp_arg = np.clip(-np.abs(v_ca) / v_tun, -50, 50)
            i_tun += JTUNSW * (v_ca - VB) * np.exp(exp_arg)

        i_total += i_tun

    # 3. Breakdown current (avalanche multiplication)
    if BV is not None and BV > 1.0:  # Only apply if BV is reasonable (> 1V)
        # Avalanche breakdown model
        reverse_mask = v_ca < -BV
        if np.any(reverse_mask):
            # Exponential breakdown current with numerical stability
            v_breakdown = v_ca[reverse_mask]
            # Limit the exponential argument to prevent overflow
            exp_arg = -(v_breakdown + BV) / v_t
            exp_arg = np.clip(exp_arg, -50, 50)  # Prevent overflow/underflow
            breakdown_factor = np.exp(exp_arg)
            i_breakdown = -IBV * breakdown_factor
            i_total[reverse_mask] += i_breakdown

    return i_total


def diode_junction_capacitance_spice(v_ca: np.ndarray, CJ: float, PB: float, MJ: float,
                                   FC: float = 0.5, CJP: float = 0.0, PHP: float = 0.75,
                                   MJSW: float = 0.33) -> np.ndarray:
    """SPICE Level=3 diode capacitance model

    Total capacitance: C_total = C_j + C_jsw

    Args:
        v_ca: Cathode-Anode voltage [V]
        CJ: Zero-bias junction capacitance [F]
        PB: Junction contact potential [V] (built-in potential)
        MJ: Junction grading coefficient
        FC: Forward-bias depletion capacitance coefficient (default 0.5)
        CJP: Zero-bias sidewall capacitance [F]
        PHP: Sidewall contact potential [V]
        MJSW: Sidewall grading coefficient

    Returns:
        c_total: Total diode capacitance [F]
    """
    c_total = np.zeros_like(v_ca)

    # 1. Junction capacitance (C_j)
    if CJ > 0:
        fc_pb = FC * PB  # Forward-bias threshold

        # Region 1: VD < FC * PB (normal depletion)
        mask1 = v_ca < fc_pb
        if np.any(mask1):
            # Avoid division by zero and negative values under sqrt
            denominator = np.maximum(1 - v_ca[mask1] / PB, 1e-10)
            c_total[mask1] += CJ * np.power(denominator, -MJ)

        # Region 2: VD >= FC * PB (forward bias, linear approximation)
        mask2 = v_ca >= fc_pb
        if np.any(mask2):
            # Linear approximation for forward bias
            c_fc = CJ * np.power(1 - FC, -MJ)  # Capacitance at FC*PB
            factor = CJ / ((1 - FC) * (1 + MJ))
            linear_term = 1 - FC * (1 + MJ) + MJ * v_ca[mask2] / PB
            c_total[mask2] += factor * linear_term

    # 2. Sidewall capacitance (C_jsw)
    if CJP > 0:
        # Sidewall capacitance (simpler model, usually no FC correction)
        denominator = np.maximum(1 - v_ca / PHP, 1e-10)
        c_total += CJP * np.power(denominator, -MJSW)

    return c_total


def diode_temperature_scaling_spice(IS_ref: float, T: float, T_ref: float = 298.15,
                                  EG: float = 1.11, XTI: float = 3.0, N: float = 1.0,
                                  GAP1: float = 7.02e-4, GAP2: float = 1108.0) -> dict:
    """SPICE Level=3 temperature scaling for diode parameters

    Args:
        IS_ref: Reference saturation current [A] at T_ref
        T: Operating temperature [K]
        T_ref: Reference temperature [K] (default 298.15K = 25°C)
        EG: Bandgap energy [eV] (default 1.11 for Si)
        XTI: Temperature exponent for IS (default 3.0)
        N: Ideality factor
        GAP1, GAP2: Bandgap temperature coefficients for Si

    Returns:
        dict: Temperature-scaled parameters
    """
    # Temperature ratio
    t_ratio = T / T_ref

    # Bandgap temperature dependence (Varshni equation)
    EG_T_ref = EG - GAP1 * T_ref**2 / (T_ref + GAP2)
    EG_T = EG - GAP1 * T**2 / (T + GAP2)

    # Thermal voltage
    v_t_ref = const.k * T_ref / const.e
    v_t = const.k * T / const.e

    # Saturation current temperature scaling
    # IS(T) = IS(T_ref) * (T/T_ref)^(XTI/N) * exp[q*EG/(N*k) * (1/T_ref - 1/T)]
    exp_arg = const.e * EG_T_ref / (N * const.k) * (1/T_ref - 1/T)
    exp_arg = np.clip(exp_arg, -50, 50)  # Prevent overflow

    IS_T = IS_ref * np.power(t_ratio, XTI/N) * np.exp(exp_arg)

    # Junction potential temperature scaling (simplified)
    # PB(T) = PB(T_ref) * T/T_ref - 2*Vt*ln(T/T_ref)
    # For simplicity, use linear approximation: PB(T) ≈ PB(T_ref) * T/T_ref
    PB_scale = t_ratio

    return {
        'IS_T': IS_T,
        'PB_scale': PB_scale,
        'EG_T': EG_T,
        'v_t': v_t,
        't_ratio': t_ratio
    }




def diode_saturation_current(i_s_0: float, T: float) -> float:
    """Temperature dependent saturation current.

    As described by Michael Schroter in the HICUM Model:
    https://www.iee.et.tu-dresden.de/iee/eb/forsch/Hicum_PD/HicumQ/hicumL2V2p4p0.va
    (line 1150) for an internal Base collector diode saturation current:
    ibcis_t = ibcis*exp(zetabci*ln_qtt0+vgc/VT*(qtt0-1))

    Args:
        i_s_0 (float): I_S [A] at T=NOMINAL_TEMPERATURE_K.
        T (float): Temperature [K].

    Returns:
        float: I_S(T) [A]
    """
    vgc = SILICON_BANDGAP_VOLTAGE_EV  # Collector bandgap voltage silicon
    VT = (const.k * T) / const.e
    qtt0 = T / NOMINAL_TEMPERATURE_K
    i_s = i_s_0 * np.exp(vgc/VT * (qtt0-1))
    return i_s


def diode_saturation_current_log(i_s_0_log: float, T: float, zeta: float) -> float:
    """Log of temperature dependent saturation current.

    As described by Michael Schroter in the HICUM Model:
    https://www.iee.et.tu-dresden.de/iee/eb/forsch/Hicum_PD/HicumQ/hicumL2V2p4p0.va
    (line 1150) for an internal Base collector diode saturation current:
    ibcis_t = ibcis*exp(zetabci*ln_qtt0+vgc/VT*(qtt0-1))
    Args:
        i_s_0 (float): I_S [A] at T=NOMINAL_TEMPERATURE_K.
        T (float): Temperature [K].

    Returns:
        float: I_S(T) [A]
    """
    i_s_log = i_s_0_log + zeta * np.log(T/NOMINAL_TEMPERATURE_K) + ((SILICON_BANDGAP_VOLTAGE_EV*const.e) / (const.k*T)) * (T/NOMINAL_TEMPERATURE_K-1)
    return i_s_log


def diode_saturation_current_0(i_s: float, T: float) -> float:
    """Saturation current at nominal temperature.

    As described by Michael Schroter in hicumL2V2p4p0_internal.va
    (line 1150) for an internal Base collector diode saturation current:
    ibcis_t = ibcis*exp(zetabci*ln_qtt0+vgc/VT*(qtt0-1))
    Args:
        i_s (float): I_S [A] at T.
        T (float): Temperature [K].

    Returns:
        float: I_S(T=NOMINAL_TEMPERATURE_K) [A]
    """
    vgc = SILICON_BANDGAP_VOLTAGE_EV  # Bandgap voltage silicon
    VT = (const.k * NOMINAL_TEMPERATURE_K) / const.e
    qtt0 = T / NOMINAL_TEMPERATURE_K
    exponent = vgc/VT * (qtt0-1)
    i_s_0 = i_s / np.exp(exponent)     # Better results
    return i_s_0


def calc_rd_deriv_a(v_ca_a: np.ndarray, i_c_a: np.ndarray) -> np.ndarray:
    """Resistance array as a derivative of voltage over current.

    Args:
        v_ca_a  (np.ndarray): Cathode-Anode voltage [V].
        i_c_a  (np.ndarray): Diode current.

    Returns:
        np.ndarray: Differential resistance r_D [Ohm]
    """
    r_D = np.gradient(v_ca_a , i_c_a )
    return r_D
