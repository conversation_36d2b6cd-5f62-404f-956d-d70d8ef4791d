""" Physics equations describing diode behavior
"""
import numpy as np
from scipy.optimize import curve_fit
import scipy.constants as const
from scipy.special import lambertw
from diode_utils import *
from config_loader import load_config

_config = load_config()

_config = load_config()

_config = load_config()

_config = load_config()

_config = load_config()

_config = load_config()

# --- HSPICE Level=3 Diode Model Constants ---
if _config and 'ModelDefaults' in _config:
    NOMINAL_TEMPERATURE_K = _config.getfloat('ModelDefaults', 'nominal_temperature_k', fallback=300.15)
    SILICON_BANDGAP_VOLTAGE_EV = _config.getfloat('ModelDefaults', 'silicon_bandgap_voltage_ev', fallback=1.17)
    DEFAULT_FC = _config.getfloat('ModelDefaults', 'default_fc', fallback=0.5)
    DEFAULT_CJO = _config.getfloat('ModelDefaults', 'default_cjo', fallback=1e-12)
    DEFAULT_VJ = _config.getfloat('ModelDefaults', 'default_vj', fallback=0.7)
    DEFAULT_M = _config.getfloat('ModelDefaults', 'default_m', fallback=0.5)  # HSPICE Level=3: M (grading coefficient)
    DEFAULT_TT = _config.getfloat('ModelDefaults', 'default_tt', fallback=1e-9)
    DEFAULT_IBV = _config.getfloat('ModelDefaults', 'default_ibv', fallback=1e-3)
    DEFAULT_XTI = _config.getfloat('ModelDefaults', 'default_xti', fallback=3.0)
    DEFAULT_EG = _config.getfloat('ModelDefaults', 'default_eg', fallback=1.17)  # HSPICE Level=3: EG (energy gap)
    DEFAULT_IS = _config.getfloat('ModelDefaults', 'default_is', fallback=1e-14)  # HSPICE Level=3: IS (saturation current)
    DEFAULT_N = _config.getfloat('ModelDefaults', 'default_n', fallback=1.0)     # HSPICE Level=3: N (ideality factor)
    DEFAULT_RS = _config.getfloat('ModelDefaults', 'default_rs', fallback=0.0)   # HSPICE Level=3: RS (series resistance)
    DEFAULT_BV = _config.getfloat('ModelDefaults', 'default_bv', fallback=1e6)   # HSPICE Level=3: BV (breakdown voltage)
else:
    # Fallback to hardcoded defaults if config is not available or section is missing
    NOMINAL_TEMPERATURE_K = 300.15
    SILICON_BANDGAP_VOLTAGE_EV = 1.17
    DEFAULT_FC = 0.5
    DEFAULT_CJO = 1e-12
    DEFAULT_VJ = 0.7
    DEFAULT_M = 0.5      # HSPICE Level=3: M (grading coefficient)
    DEFAULT_TT = 1e-9
    DEFAULT_IBV = 1e-3
    DEFAULT_XTI = 3.0
    DEFAULT_EG = 1.17    # HSPICE Level=3: EG (energy gap)
    DEFAULT_IS = 1e-14   # HSPICE Level=3: IS (saturation current)
    DEFAULT_N = 1.0      # HSPICE Level=3: N (ideality factor)
    DEFAULT_RS = 0.0     # HSPICE Level=3: RS (series resistance)
    DEFAULT_BV = 1e6     # HSPICE Level=3: BV (breakdown voltage)

# Legacy aliases for backward compatibility
DEFAULT_M_GRADING = DEFAULT_M

# --- Diode Equations ---

def ideal_diode_eq(v_ca: float, IS: float, N: float, T: float) -> float:
    """Shockley Diode equation (HSPICE Level=3 compatible)

    Args:
        v_ca (float): Cathode-Anode voltage [V].
        IS (float): Saturation current [A].
        N (float): Ideality factor.
        T (float): Temperature [K].

    Returns:
        float: Diode current I_C [A]
    """
    i_c = IS * (np.exp((v_ca * const.e)/(N * const.k * T)) - 1)
    return i_c

def get_ideal_diode_eq_log(T: float):
    """Returns a function for the logarithmic Shockley Diode equation,
    suitable for curve fitting (HSPICE Level=3 compatible).

    The returned function calculates log(I_C) based on V_CA, log(IS), and N.
    It assumes V_CA >> V_T, simplifying the Shockley equation to:
    I_C approxeq IS * exp(V_CA / (V_T * N))
    Taking the natural logarithm:
    log(I_C) approxeq log(IS) + V_CA / (V_T * N)

    Args:
        T (float): Temperature in Kelvin.

    Returns:
        Callable[[float, float, float], float]: A function that takes
            (v_ca, IS_log, N) and returns log(i_c).
    """
    def _ideal_diode_eq_log_internal(v_ca: float, IS_log: float, N: float) -> float:
        """Internal function for the logarithmic Shockley Diode equation.

        Args:
            v_ca (float): Cathode-Anode voltage [V].
            IS_log (float): Natural logarithm of the saturation current [ln(A)].
            N (float): Ideality factor.

        Returns:
            float: Natural logarithm of the diode current [ln(A)].
        """
        # Thermal voltage V_T = k * T / q
        # The term V_CA / (V_T * N) can be rewritten as (V_CA * q) / (k * T * N)
        i_c_log = IS_log + v_ca * (const.e / (N * const.k * T))
        return i_c_log
    return _ideal_diode_eq_log_internal


def ic_diode_complete(v_ca: np.ndarray, IS: float, N: float, T: float, RS: float, BV: float, IBV: float) -> np.ndarray:
    """Calculate current of a diode with series resistance and reverse breakdown (HSPICE Level=3 compatible).

    Args:
        v_ca (float): Cathode-Anode voltage [V].
        IS (float): Saturation current [A].
        N (float): Ideality factor.
        T (float): Temperature [K].
        RS (float): Ohmic diode resistance [Ohm].
        BV (float): Reverse breakdown voltage [V].
        IBV (float): Current at breakdown voltage [A].

    Returns:
        i_c (float): Diode current [A].
    """
    # Forward current component (same as ic_diode_ohmic)
    v_t = (const.k * T * N) / const.e
    i_c_forward = ((-IS * RS + v_t * lambertw(IS * RS * np.exp((IS * RS + v_ca) / v_t) / v_t)) / RS)
    i_c_forward = np.real(i_c_forward)

    # Reverse breakdown current component
    # A simple exponential model for breakdown
    i_c_breakdown = -IBV * np.exp(-(v_ca + BV) / (N * v_t))

    return i_c_forward + i_c_breakdown

def diode_saturation_current_standard(IS_nom: float, T: float, T_nom: float, EG: float, XTI: float) -> float:
    """Standard SPICE model for temperature dependent saturation current (HSPICE Level=3 compatible).

    Args:
        IS_nom (float): Saturation current at nominal temperature T_nom [A].
        T (float): Temperature [K].
        T_nom (float): Nominal temperature [K].
        EG (float): Energy gap [eV].
        XTI (float): Saturation current temperature exponent.

    Returns:
        float: IS(T) [A]
    """
    k_boltz = const.k
    q_electron = const.e

    vt_nom = k_boltz * T_nom / q_electron
    vt_t = k_boltz * T / q_electron

    factor1 = (T / T_nom)**XTI # Standard SPICE temperature dependence
    factor2 = np.exp(((EG / vt_nom) * (T / T_nom - 1)))

    IS_t = IS_nom * factor1 * factor2
    return IS_t



# Removed ohmic_resistance_diode as it was marked for deletion


def ic_diode_ohmic(v_ca: np.ndarray, IS: float, N: float, T: float, RS: float) -> np.ndarray:
    """Calculate current of an ideal diode in series with a resistor (HSPICE Level=3 compatible)

    Args:
        v_ca (float): Cathode-Anode voltage [V].
        IS (float): Saturation current [A].
        N (float): Ideality factor (model parameter)
        T (float): Temperature [K], defaults to 298.0
        RS (float): Ohmic diode resistance

    Returns:
        i_c_a  (float array): Diode current
    """
    # Ideal diode and ohmic resistance in series:
    # ln((i_c+IS)/IS) * v_t + i_c*RS -v_ca = 0
    # sympy.solve(log((x+a)/a)*b+c*x-d, x) = [(-a*c +
    #       b*LambertW(a*c*exp((a*c + d)/b)/b))/c]
    v_t = (const.k * T * N) / const.e
    i_c = ((-IS * RS + v_t * lambertw(IS*RS * np.exp((IS*RS + v_ca)/v_t)/v_t))/RS)
    i_c = np.real(i_c)      # Avoid warning; imaginary part is already zero
    return i_c


def ic_diode_spice_level3(v_ca: np.ndarray, IS: float, N: float, T: float, RS: float = 0.0,
                         BV: float = None, IBV: float = 1e-3, JTUN: float = None,
                         JTUNSW: float = None, NTUN: float = None) -> np.ndarray:
    """Complete SPICE Level=3 diode current model with breakdown and tunneling

    Args:
        v_ca: Cathode-Anode voltage [V]
        IS: Saturation current [A]
        N: Ideality factor
        T: Temperature [K]
        RS: Series resistance [Ohm]
        BV: Breakdown voltage [V] (positive value)
        IBV: Current at breakdown voltage [A]
        JTUN: Tunneling saturation current [A]
        JTUNSW: Tunneling switch current [A]
        NTUN: Tunneling ideality factor

    Returns:
        i_c_a: Total diode current [A]
    """
    v_t = const.k * T / const.e  # Thermal voltage

    # Handle None parameters with defaults
    if JTUN is None:
        JTUN = 0.0
    if JTUNSW is None:
        JTUNSW = 0.0
    if NTUN is None:
        NTUN = 30.0
    if RS is None:
        RS = 0.0
    if IBV is None:
        IBV = 1e-3

    # Initialize current array
    i_total = np.zeros_like(v_ca)

    # 1. Forward and reverse bias normal diode current
    if RS > 0:
        # Use Lambert W function for series resistance
        try:
            i_diode = ic_diode_ohmic(v_ca, IS, N, T, RS)
            # Check for invalid values and fallback if needed
            if np.any(np.isinf(i_diode)) or np.any(np.isnan(i_diode)):
                raise ValueError("Lambert W produced invalid values")
        except:
            # Fallback to simple exponential if Lambert W fails
            exp_arg = np.clip(v_ca / (N * v_t), -50, 50)  # Prevent overflow
            i_diode = IS * (np.exp(exp_arg) - 1)
    else:
        # Simple exponential diode equation with numerical stability
        exp_arg = np.clip(v_ca / (N * v_t), -50, 50)  # Prevent overflow
        i_diode = IS * (np.exp(exp_arg) - 1)

    i_total += i_diode

    # 2. Tunneling current (important for reverse bias)
    if JTUN is not None and JTUN > 0:
        # Band-to-band tunneling current (Zener-like)
        v_tun = NTUN * v_t if NTUN is not None else 30.0 * v_t
        if JTUNSW is not None and JTUNSW > 0:
            # Smooth transition tunneling model with numerical stability
            exp_arg1 = np.clip(-v_ca / v_tun, -50, 50)
            exp_arg2 = np.clip((JTUNSW - np.abs(v_ca)) / v_tun, -50, 50)
            i_tunnel = JTUN * (np.exp(exp_arg1) - 1) * (1 + v_ca / v_tun)
            # Apply switching function
            switch_factor = 1 / (1 + np.exp(exp_arg2))
            i_tunnel *= switch_factor
        else:
            # Simple tunneling model with numerical stability
            exp_arg = np.clip(-np.abs(v_ca) / v_tun, -50, 50)
            i_tunnel = -JTUN * np.exp(exp_arg) * np.sign(v_ca)

        i_total += i_tunnel

    # 3. Breakdown current (avalanche multiplication)
    if BV is not None and BV > 1.0:  # Only apply if BV is reasonable (> 1V)
        # Avalanche breakdown model
        reverse_mask = v_ca < -BV
        if np.any(reverse_mask):
            # Exponential breakdown current with numerical stability
            v_breakdown = v_ca[reverse_mask]
            # Limit the exponential argument to prevent overflow
            exp_arg = -(v_breakdown + BV) / v_t
            exp_arg = np.clip(exp_arg, -50, 50)  # Prevent overflow/underflow
            breakdown_factor = np.exp(exp_arg)
            i_breakdown = -IBV * breakdown_factor
            i_total[reverse_mask] += i_breakdown

    return i_total




def diode_saturation_current(i_s_0: float, T: float) -> float:
    """Temperature dependent saturation current.

    As described by Michael Schroter in the HICUM Model:
    https://www.iee.et.tu-dresden.de/iee/eb/forsch/Hicum_PD/HicumQ/hicumL2V2p4p0.va
    (line 1150) for an internal Base collector diode saturation current:
    ibcis_t = ibcis*exp(zetabci*ln_qtt0+vgc/VT*(qtt0-1))

    Args:
        i_s_0 (float): I_S [A] at T=NOMINAL_TEMPERATURE_K.
        T (float): Temperature [K].

    Returns:
        float: I_S(T) [A]
    """
    vgc = SILICON_BANDGAP_VOLTAGE_EV  # Collector bandgap voltage silicon
    VT = (const.k * T) / const.e
    qtt0 = T / NOMINAL_TEMPERATURE_K
    i_s = i_s_0 * np.exp(vgc/VT * (qtt0-1))
    return i_s


def diode_saturation_current_log(i_s_0_log: float, T: float, zeta: float) -> float:
    """Log of temperature dependent saturation current.

    As described by Michael Schroter in the HICUM Model:
    https://www.iee.et.tu-dresden.de/iee/eb/forsch/Hicum_PD/HicumQ/hicumL2V2p4p0.va
    (line 1150) for an internal Base collector diode saturation current:
    ibcis_t = ibcis*exp(zetabci*ln_qtt0+vgc/VT*(qtt0-1))
    Args:
        i_s_0 (float): I_S [A] at T=NOMINAL_TEMPERATURE_K.
        T (float): Temperature [K].

    Returns:
        float: I_S(T) [A]
    """
    i_s_log = i_s_0_log + zeta * np.log(T/NOMINAL_TEMPERATURE_K) + ((SILICON_BANDGAP_VOLTAGE_EV*const.e) / (const.k*T)) * (T/NOMINAL_TEMPERATURE_K-1)
    return i_s_log


def diode_saturation_current_0(i_s: float, T: float) -> float:
    """Saturation current at nominal temperature.

    As described by Michael Schroter in hicumL2V2p4p0_internal.va
    (line 1150) for an internal Base collector diode saturation current:
    ibcis_t = ibcis*exp(zetabci*ln_qtt0+vgc/VT*(qtt0-1))
    Args:
        i_s (float): I_S [A] at T.
        T (float): Temperature [K].

    Returns:
        float: I_S(T=NOMINAL_TEMPERATURE_K) [A]
    """
    vgc = SILICON_BANDGAP_VOLTAGE_EV  # Bandgap voltage silicon
    VT = (const.k * NOMINAL_TEMPERATURE_K) / const.e
    qtt0 = T / NOMINAL_TEMPERATURE_K
    exponent = vgc/VT * (qtt0-1)
    i_s_0 = i_s / np.exp(exponent)     # Better results
    return i_s_0


def calc_rd_deriv_a(v_ca_a: np.ndarray, i_c_a: np.ndarray) -> np.ndarray:
    """Resistance array as a derivative of voltage over current.

    Args:
        v_ca_a  (np.ndarray): Cathode-Anode voltage [V].
        i_c_a  (np.ndarray): Diode current.

    Returns:
        np.ndarray: Differential resistance r_D [Ohm]
    """
    r_D = np.gradient(v_ca_a , i_c_a )
    return r_D
