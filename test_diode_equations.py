
import unittest
import numpy as np
import scipy.constants as const
from diode_equations import ideal_diode_eq, ic_diode_ohmic, diode_saturation_current_standard, ic_diode_complete
from diode_modelling import depletion_capacitance_eq, diode_capacitance_TT_eq, total_capacitance_eq
import numpy as np
import scipy.constants as const
import unittest

class TestDiodeEquations(unittest.TestCase):

    def test_ideal_diode_eq(self):
        v_ca = 0.7
        i_s = 1e-12
        m = 1.1
        T = 300.0
        expected_i_c = i_s * (np.exp((v_ca * const.e)/(m * const.k * T)) - 1)
        self.assertAlmostEqual(ideal_diode_eq(v_ca, i_s, m, T), expected_i_c)

    def test_ic_diode_ohmic(self):
        v_ca = np.array([0.7])
        i_s = 1e-12
        m = 1.1
        T = 300.0
        r_s = 10.0
        # This is a complex equation, so we'll test against a known good value or a simplified case
        # For simplicity, let's assume a very small r_s for comparison with ideal_diode_eq
        # When r_s is very small, ic_diode_ohmic should approach ideal_diode_eq
        i_c_ohmic = ic_diode_ohmic(v_ca, i_s, m, T, 1e-9) # Very small r_s
        i_c_ideal = ideal_diode_eq(v_ca, i_s, m, T)
        self.assertAlmostEqual(i_c_ohmic[0], i_c_ideal[0], places=5)

    def test_diode_capacitance_TT_eq(self):
        i_c = 0.01
        tt = 1e-9
        expected_c_ca = tt * i_c
        self.assertAlmostEqual(diode_capacitance_TT_eq(i_c, tt), expected_c_ca)

    def test_depletion_capacitance_eq(self):
        v_ca = 0.5
        cjo = 1e-12
        vj = 0.7
        m_grading = 0.5
        fc = 0.5
        # Test case where v_ca < fc * vj
        expected_c_dep_1 = cjo / ((1 - v_ca / vj)**m_grading)
        self.assertAlmostEqual(depletion_capacitance_eq(v_ca, cjo, vj, m_grading, fc), expected_c_dep_1)

        # Test case where v_ca >= fc * vj
        v_ca_2 = 0.6
        expected_c_dep_2 = cjo / ((1 - fc)**m_grading) * (1 + m_grading * (v_ca_2 - fc * vj) / (vj * (1 - fc)))
        self.assertAlmostEqual(depletion_capacitance_eq(v_ca_2, cjo, vj, m_grading, fc), expected_c_dep_2)

    def test_total_capacitance_eq(self):
        v_ca = 0.7
        cjo = 1e-12
        vj = 0.7
        m_grading = 0.5
        fc = 0.5
        tt = 1e-9
        i_c = 0.01
        c_diff = diode_capacitance_TT_eq(i_c, tt)
        c_dep = depletion_capacitance_eq(v_ca, cjo, vj, m_grading, fc)
        expected_total_c = c_diff + c_dep
        self.assertAlmostEqual(total_capacitance_eq(v_ca, cjo, vj, m_grading, fc, tt, i_c), expected_total_c)

    def test_diode_saturation_current_standard(self):
        i_s_nom = 1e-12
        T = 350.0
        T_nom = 300.0
        eg = 1.12
        xti = 3.0
        # This is a complex equation, so we'll test against a known good value or a simplified case
        # For simplicity, if T = T_nom, i_s_t should be i_s_nom
        i_s_t_at_nom = diode_saturation_current_standard(i_s_nom, T_nom, T_nom, eg, xti)
        self.assertAlmostEqual(i_s_t_at_nom, i_s_nom)

    def test_ic_diode_complete(self):
        v_ca = np.array([-1.0, 0.5, 20.0])
        i_s = 1e-12
        m = 1.1
        T = 300.0
        r_S = 10.0
        bv = 10.0
        ibv = 1e-3
        # This is a complex equation, so we'll test against a known good value or a simplified case
        # For simplicity, if bv is very high, it should behave like ic_diode_ohmic
        i_c_complete_high_bv = ic_diode_complete(v_ca, i_s, m, T, r_S, 1e9, ibv) # Very high bv
        i_c_ohmic_val = ic_diode_ohmic(v_ca, i_s, m, T, r_S)
        np.testing.assert_almost_equal(i_c_complete_high_bv, i_c_ohmic_val, decimal=5)

if __name__ == '__main__':
    unittest.main()
