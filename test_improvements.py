#!/usr/bin/env python3
"""
测试GUI改进功能
"""

import sys
import os

def test_gui_improvements():
    """测试GUI改进功能"""
    print("🔍 测试GUI改进功能...")
    
    try:
        from diode_pyqt_gui import DiodeParameterExtractorGUI
        import inspect
        
        # 检查新增的方法
        methods_to_check = [
            '_toggle_sidebar',
            '_toggle_fullscreen_plot', 
            '_setup_shortcuts'
        ]
        
        for method_name in methods_to_check:
            if hasattr(DiodeParameterExtractorGUI, method_name):
                print(f"✅ {method_name} 方法已添加")
            else:
                print(f"❌ {method_name} 方法缺失")
                return False
        
        # 检查新增的属性
        # 创建一个临时实例来检查属性（不显示GUI）
        from PyQt5.QtWidgets import QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # 检查窗口尺寸设置
        source = inspect.getsource(DiodeParameterExtractorGUI.__init__)
        if "1920, 1080" in source:
            print("✅ 窗口尺寸已增大到1920x1080")
        else:
            print("❌ 窗口尺寸未正确设置")
            return False
            
        # 检查布局比例设置 - 检查整个类的源码
        class_source = inspect.getsource(DiodeParameterExtractorGUI)
        if "setSizes([350, 1570])" in class_source:
            print("✅ 布局比例已优化")
        else:
            print("❌ 布局比例未正确设置")
            return False
            
        # 检查快捷键设置
        shortcut_source = inspect.getsource(DiodeParameterExtractorGUI._setup_shortcuts)
        shortcuts = ["F11", "Ctrl+H", "Ctrl+T", "Ctrl+R", "Left", "Right"]
        missing_shortcuts = []
        
        for shortcut in shortcuts:
            if shortcut not in shortcut_source:
                missing_shortcuts.append(shortcut)
        
        if missing_shortcuts:
            print(f"❌ 缺失快捷键: {', '.join(missing_shortcuts)}")
            return False
        else:
            print("✅ 所有快捷键已正确设置")
            
        # 检查图表尺寸改进
        render_source = inspect.getsource(DiodeParameterExtractorGUI._render_plots)
        if "figure_width" in render_source and "figure_height" in render_source:
            print("✅ 图表尺寸动态调整已实现")
        else:
            print("❌ 图表尺寸改进未实现")
            return False
            
        print("✅ 所有GUI改进功能验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_layout_calculations():
    """测试布局计算"""
    print("🔍 测试布局计算...")
    
    # 原始布局
    original_total = 1600
    original_plot_area = 1200
    original_sidebar = 400
    
    # 改进后布局
    improved_total = 1920
    improved_plot_area = 1570
    improved_sidebar = 350
    
    # 计算改进幅度
    plot_area_increase = (improved_plot_area - original_plot_area) / original_plot_area * 100
    total_area_increase = (improved_total - original_total) / original_total * 100
    
    print(f"📊 布局改进统计:")
    print(f"  总窗口宽度: {original_total}px → {improved_total}px (+{total_area_increase:.1f}%)")
    print(f"  绘图区域宽度: {original_plot_area}px → {improved_plot_area}px (+{plot_area_increase:.1f}%)")
    print(f"  侧边栏宽度: {original_sidebar}px → {improved_sidebar}px")
    
    if plot_area_increase > 25:  # 期望至少增加25%
        print("✅ 绘图区域显著增大")
        return True
    else:
        print("❌ 绘图区域增大不够显著")
        return False

def main():
    """主测试函数"""
    print("🚀 GUI改进功能测试")
    print("=" * 50)
    
    # 测试改进功能
    improvements_ok = test_gui_improvements()
    
    # 测试布局计算
    layout_ok = test_layout_calculations()
    
    print("=" * 50)
    
    if improvements_ok and layout_ok:
        print("🎉 所有GUI改进测试通过！")
        print("\n📋 新功能使用指南:")
        print("  F11        - 全屏绘图模式")
        print("  Ctrl+H     - 隐藏/显示侧边栏")
        print("  Ctrl+T     - 切换主题")
        print("  Ctrl+R     - 运行参数提取")
        print("  ← →        - 切换图表页面")
        print("  Ctrl+Q     - 退出程序")
        print("\n🎯 改进亮点:")
        print("  • 绘图区域增加31%")
        print("  • 窗口尺寸增大到1920x1080")
        print("  • 支持全屏和侧边栏折叠")
        print("  • 丰富的键盘快捷键")
        print("  • 更大更清晰的图表")
        return 0
    else:
        print("❌ 部分测试失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
